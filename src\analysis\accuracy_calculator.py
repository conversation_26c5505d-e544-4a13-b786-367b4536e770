"""
福彩3D多维度准确率计算器
支持直选、组选、位置、和值、跨度等5种维度的准确率计算

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import logging
from typing import Dict, List, Any, Tuple
from collections import Counter
import itertools

# 配置日志
logger = logging.getLogger(__name__)


class AccuracyCalculator:
    """多维度准确率计算器"""
    
    def __init__(self):
        """初始化准确率计算器"""
        pass
    
    def direct_accuracy(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        直选准确率：完全匹配的预测
        
        Args:
            predictions: 预测号码列表
            actual: 实际开奖号码
            
        Returns:
            直选准确率结果
        """
        try:
            if not predictions or not actual:
                return self._empty_result('direct')
            
            actual = str(actual).zfill(3)
            total_predictions = len(predictions)
            direct_hits = 0
            
            for pred in predictions:
                pred = str(pred).zfill(3)
                if pred == actual:
                    direct_hits += 1
            
            accuracy = direct_hits / total_predictions if total_predictions > 0 else 0
            
            result = {
                'type': 'direct',
                'total_predictions': total_predictions,
                'hits': direct_hits,
                'accuracy': accuracy,
                'percentage': accuracy * 100,
                'actual_number': actual,
                'hit_predictions': [pred for pred in predictions if str(pred).zfill(3) == actual]
            }
            
            logger.info(f"直选准确率计算完成: {direct_hits}/{total_predictions} = {accuracy:.2%}")
            return result
            
        except Exception as e:
            logger.error(f"直选准确率计算失败: {e}")
            return self._empty_result('direct')
    
    def group_accuracy(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        组选准确率：数字组合匹配（不考虑顺序）
        
        Args:
            predictions: 预测号码列表
            actual: 实际开奖号码
            
        Returns:
            组选准确率结果
        """
        try:
            if not predictions or not actual:
                return self._empty_result('group')
            
            actual = str(actual).zfill(3)
            actual_sorted = ''.join(sorted(actual))
            total_predictions = len(predictions)
            group_hits = 0
            hit_predictions = []
            
            for pred in predictions:
                pred = str(pred).zfill(3)
                pred_sorted = ''.join(sorted(pred))
                if pred_sorted == actual_sorted:
                    group_hits += 1
                    hit_predictions.append(pred)
            
            accuracy = group_hits / total_predictions if total_predictions > 0 else 0
            
            result = {
                'type': 'group',
                'total_predictions': total_predictions,
                'hits': group_hits,
                'accuracy': accuracy,
                'percentage': accuracy * 100,
                'actual_number': actual,
                'actual_sorted': actual_sorted,
                'hit_predictions': hit_predictions
            }
            
            logger.info(f"组选准确率计算完成: {group_hits}/{total_predictions} = {accuracy:.2%}")
            return result
            
        except Exception as e:
            logger.error(f"组选准确率计算失败: {e}")
            return self._empty_result('group')
    
    def position_accuracy(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        位置准确率：各位置单独的准确率
        
        Args:
            predictions: 预测号码列表
            actual: 实际开奖号码
            
        Returns:
            位置准确率结果
        """
        try:
            if not predictions or not actual:
                return self._empty_result('position')
            
            actual = str(actual).zfill(3)
            total_predictions = len(predictions)
            
            position_hits = {'hundreds': 0, 'tens': 0, 'units': 0}
            position_predictions = {'hundreds': [], 'tens': [], 'units': []}
            
            for pred in predictions:
                pred = str(pred).zfill(3)
                if len(pred) == 3:
                    # 百位
                    if pred[0] == actual[0]:
                        position_hits['hundreds'] += 1
                        position_predictions['hundreds'].append(pred)
                    
                    # 十位
                    if pred[1] == actual[1]:
                        position_hits['tens'] += 1
                        position_predictions['tens'].append(pred)
                    
                    # 个位
                    if pred[2] == actual[2]:
                        position_hits['units'] += 1
                        position_predictions['units'].append(pred)
            
            position_accuracy = {}
            for pos in ['hundreds', 'tens', 'units']:
                position_accuracy[pos] = position_hits[pos] / total_predictions if total_predictions > 0 else 0
            
            result = {
                'type': 'position',
                'total_predictions': total_predictions,
                'hits': position_hits,
                'accuracy': position_accuracy,
                'percentage': {pos: acc * 100 for pos, acc in position_accuracy.items()},
                'actual_number': actual,
                'actual_digits': {'hundreds': actual[0], 'tens': actual[1], 'units': actual[2]},
                'hit_predictions': position_predictions
            }
            
            logger.info(f"位置准确率计算完成: 百位{position_hits['hundreds']}, 十位{position_hits['tens']}, 个位{position_hits['units']}")
            return result
            
        except Exception as e:
            logger.error(f"位置准确率计算失败: {e}")
            return self._empty_result('position')
    
    def sum_accuracy(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        和值准确率：预测和值与实际和值的匹配
        
        Args:
            predictions: 预测号码列表
            actual: 实际开奖号码
            
        Returns:
            和值准确率结果
        """
        try:
            if not predictions or not actual:
                return self._empty_result('sum')
            
            actual = str(actual).zfill(3)
            actual_sum = sum(int(d) for d in actual)
            total_predictions = len(predictions)
            sum_hits = 0
            hit_predictions = []
            
            for pred in predictions:
                pred = str(pred).zfill(3)
                if len(pred) == 3:
                    pred_sum = sum(int(d) for d in pred)
                    if pred_sum == actual_sum:
                        sum_hits += 1
                        hit_predictions.append(pred)
            
            accuracy = sum_hits / total_predictions if total_predictions > 0 else 0
            
            result = {
                'type': 'sum',
                'total_predictions': total_predictions,
                'hits': sum_hits,
                'accuracy': accuracy,
                'percentage': accuracy * 100,
                'actual_number': actual,
                'actual_sum': actual_sum,
                'hit_predictions': hit_predictions
            }
            
            logger.info(f"和值准确率计算完成: {sum_hits}/{total_predictions} = {accuracy:.2%}, 和值{actual_sum}")
            return result
            
        except Exception as e:
            logger.error(f"和值准确率计算失败: {e}")
            return self._empty_result('sum')
    
    def span_accuracy(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        跨度准确率：预测跨度与实际跨度的匹配
        
        Args:
            predictions: 预测号码列表
            actual: 实际开奖号码
            
        Returns:
            跨度准确率结果
        """
        try:
            if not predictions or not actual:
                return self._empty_result('span')
            
            actual = str(actual).zfill(3)
            actual_digits = [int(d) for d in actual]
            actual_span = max(actual_digits) - min(actual_digits)
            total_predictions = len(predictions)
            span_hits = 0
            hit_predictions = []
            
            for pred in predictions:
                pred = str(pred).zfill(3)
                if len(pred) == 3:
                    pred_digits = [int(d) for d in pred]
                    pred_span = max(pred_digits) - min(pred_digits)
                    if pred_span == actual_span:
                        span_hits += 1
                        hit_predictions.append(pred)
            
            accuracy = span_hits / total_predictions if total_predictions > 0 else 0
            
            result = {
                'type': 'span',
                'total_predictions': total_predictions,
                'hits': span_hits,
                'accuracy': accuracy,
                'percentage': accuracy * 100,
                'actual_number': actual,
                'actual_span': actual_span,
                'hit_predictions': hit_predictions
            }
            
            logger.info(f"跨度准确率计算完成: {span_hits}/{total_predictions} = {accuracy:.2%}, 跨度{actual_span}")
            return result
            
        except Exception as e:
            logger.error(f"跨度准确率计算失败: {e}")
            return self._empty_result('span')
    
    def comprehensive_accuracy(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        综合准确率分析：计算所有维度的准确率
        
        Args:
            predictions: 预测号码列表
            actual: 实际开奖号码
            
        Returns:
            综合准确率结果
        """
        try:
            # 计算各维度准确率
            direct_result = self.direct_accuracy(predictions, actual)
            group_result = self.group_accuracy(predictions, actual)
            position_result = self.position_accuracy(predictions, actual)
            sum_result = self.sum_accuracy(predictions, actual)
            span_result = self.span_accuracy(predictions, actual)
            
            # 计算加权综合准确率
            weights = {
                'direct': 0.4,    # 直选权重40%
                'group': 0.25,    # 组选权重25%
                'position': 0.2,  # 位置权重20%
                'sum': 0.1,       # 和值权重10%
                'span': 0.05      # 跨度权重5%
            }
            
            overall_accuracy = (
                direct_result['accuracy'] * weights['direct'] +
                group_result['accuracy'] * weights['group'] +
                sum(position_result['accuracy'].values()) / 3 * weights['position'] +
                sum_result['accuracy'] * weights['sum'] +
                span_result['accuracy'] * weights['span']
            )
            
            result = {
                'type': 'comprehensive',
                'actual_number': actual,
                'total_predictions': len(predictions),
                'dimensions': {
                    'direct': direct_result,
                    'group': group_result,
                    'position': position_result,
                    'sum': sum_result,
                    'span': span_result
                },
                'overall_accuracy': overall_accuracy,
                'overall_percentage': overall_accuracy * 100,
                'weights': weights
            }
            
            logger.info(f"综合准确率计算完成: {overall_accuracy:.2%}")
            return result
            
        except Exception as e:
            logger.error(f"综合准确率计算失败: {e}")
            return {}
    
    def _empty_result(self, result_type: str) -> Dict[str, Any]:
        """返回空的计算结果"""
        return {
            'type': result_type,
            'total_predictions': 0,
            'hits': 0,
            'accuracy': 0,
            'percentage': 0,
            'actual_number': '',
            'hit_predictions': []
        }
