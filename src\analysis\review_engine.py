"""
福彩3D复盘分析引擎
实现预测结果与开奖结果的精确对比，替换硬编码的65%准确率

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import sqlite3

# 导入开奖号码查询引擎
from ..data.lottery_query import LotteryQueryEngine

# 配置日志
logger = logging.getLogger(__name__)


class ReviewEngine:
    """复盘分析引擎"""

    def __init__(self, db_path: str = "data/lottery.db"):
        """
        初始化复盘引擎

        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.lottery_query = LotteryQueryEngine(db_path)

    def compare_predictions(self, predictions: List[str], actual: str) -> Dict[str, Any]:
        """
        比较预测结果与实际开奖结果

        Args:
            predictions: 预测号码列表 (如: ["013", "014", "015", ...])
            actual: 实际开奖号码 (如: "123")

        Returns:
            对比结果字典
        """
        try:
            if not predictions or not actual:
                logger.warning("预测结果或实际开奖号码为空")
                return self._empty_comparison_result()

            # 确保actual是3位字符串
            actual = str(actual).zfill(3)
            if len(actual) != 3:
                logger.error(f"实际开奖号码格式错误: {actual}")
                return self._empty_comparison_result()

            # 分析预测结果
            total_predictions = len(predictions)
            direct_hits = 0  # 直选命中
            group_hits = 0   # 组选命中
            position_hits = {'hundreds': 0, 'tens': 0, 'units': 0}  # 位置命中

            actual_hundreds = int(actual[0])
            actual_tens = int(actual[1])
            actual_units = int(actual[2])
            actual_sorted = ''.join(sorted(actual))

            for pred in predictions:
                pred = str(pred).zfill(3)
                if len(pred) != 3:
                    continue

                # 直选命中检查
                if pred == actual:
                    direct_hits += 1

                # 组选命中检查（数字相同但顺序不同）
                pred_sorted = ''.join(sorted(pred))
                if pred_sorted == actual_sorted:
                    group_hits += 1

                # 位置命中检查
                if pred[0] == actual[0]:
                    position_hits['hundreds'] += 1
                if pred[1] == actual[1]:
                    position_hits['tens'] += 1
                if pred[2] == actual[2]:
                    position_hits['units'] += 1

            # 计算准确率
            direct_accuracy = direct_hits / total_predictions if total_predictions > 0 else 0
            group_accuracy = group_hits / total_predictions if total_predictions > 0 else 0
            position_accuracy = {
                'hundreds': position_hits['hundreds'] / total_predictions if total_predictions > 0 else 0,
                'tens': position_hits['tens'] / total_predictions if total_predictions > 0 else 0,
                'units': position_hits['units'] / total_predictions if total_predictions > 0 else 0
            }

            # 综合准确率（加权平均）
            overall_accuracy = (
                direct_accuracy * 0.5 +  # 直选权重50%
                group_accuracy * 0.3 +   # 组选权重30%
                sum(position_accuracy.values()) / 3 * 0.2  # 位置权重20%
            )

            result = {
                'actual_number': actual,
                'total_predictions': total_predictions,
                'direct_hits': direct_hits,
                'group_hits': group_hits,
                'position_hits': position_hits,
                'direct_accuracy': direct_accuracy,
                'group_accuracy': group_accuracy,
                'position_accuracy': position_accuracy,
                'overall_accuracy': overall_accuracy,
                'comparison_time': datetime.now().isoformat()
            }

            logger.info(f"复盘对比完成: 直选{direct_hits}/{total_predictions}, 组选{group_hits}/{total_predictions}, 综合准确率{overall_accuracy:.2%}")
            return result

        except Exception as e:
            logger.error(f"预测对比失败: {e}")
            return self._empty_comparison_result()

    def analyze_prediction_quality(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析多期预测质量

        Args:
            results: 多期对比结果列表

        Returns:
            质量分析结果
        """
        try:
            if not results:
                return {
                    'total_periods': 0,
                    'average_accuracy': 0,
                    'best_accuracy': 0,
                    'worst_accuracy': 0,
                    'trend': 'stable',
                    'improvement_suggestions': []
                }

            # 计算统计数据
            total_periods = len(results)
            accuracies = [r.get('overall_accuracy', 0) for r in results]

            average_accuracy = sum(accuracies) / total_periods
            best_accuracy = max(accuracies)
            worst_accuracy = min(accuracies)

            # 分析趋势
            trend = self._analyze_trend(accuracies)

            # 生成改进建议
            suggestions = self._generate_improvement_suggestions(results)

            analysis = {
                'total_periods': total_periods,
                'average_accuracy': average_accuracy,
                'best_accuracy': best_accuracy,
                'worst_accuracy': worst_accuracy,
                'trend': trend,
                'improvement_suggestions': suggestions,
                'analysis_time': datetime.now().isoformat()
            }

            logger.info(f"预测质量分析完成: 平均准确率{average_accuracy:.2%}, 趋势{trend}")
            return analysis

        except Exception as e:
            logger.error(f"预测质量分析失败: {e}")
            return {}

    def generate_review_summary(self, review_data: Dict[str, Any]) -> str:
        """
        生成复盘摘要报告

        Args:
            review_data: 复盘数据

        Returns:
            摘要报告字符串
        """
        try:
            if not review_data:
                return "复盘数据为空，无法生成摘要"

            actual = review_data.get('actual_number', 'N/A')
            total = review_data.get('total_predictions', 0)
            direct_hits = review_data.get('direct_hits', 0)
            group_hits = review_data.get('group_hits', 0)
            overall_accuracy = review_data.get('overall_accuracy', 0)

            position_hits = review_data.get('position_hits', {})
            h_hits = position_hits.get('hundreds', 0)
            t_hits = position_hits.get('tens', 0)
            u_hits = position_hits.get('units', 0)

            summary = f"""
📊 复盘分析摘要
🎲 实际开奖: {actual}
🎯 预测总数: {total}

📈 命中统计:
  - 直选命中: {direct_hits}/{total} ({direct_hits/total*100:.1f}%)
  - 组选命中: {group_hits}/{total} ({group_hits/total*100:.1f}%)
  - 百位命中: {h_hits}/{total} ({h_hits/total*100:.1f}%)
  - 十位命中: {t_hits}/{total} ({t_hits/total*100:.1f}%)
  - 个位命中: {u_hits}/{total} ({u_hits/total*100:.1f}%)

📊 综合准确率: {overall_accuracy:.2%}
""".strip()

            return summary

        except Exception as e:
            logger.error(f"生成复盘摘要失败: {e}")
            return f"生成摘要失败: {e}"

    def get_recent_predictions(self, issue: str, limit: int = 20) -> List[str]:
        """
        获取指定期号的预测结果

        Args:
            issue: 期号
            limit: 获取数量限制

        Returns:
            预测号码列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row

            cursor = conn.execute(
                """
                SELECT combination FROM fusion_predictions
                WHERE issue = ?
                ORDER BY rank ASC
                LIMIT ?
                """,
                (issue, limit)
            )

            results = cursor.fetchall()
            conn.close()

            predictions = [row['combination'] for row in results]
            logger.info(f"获取期号 {issue} 的预测结果: {len(predictions)} 个")
            return predictions

        except Exception as e:
            logger.error(f"获取预测结果失败: {e}")
            return []

    def _empty_comparison_result(self) -> Dict[str, Any]:
        """返回空的对比结果"""
        return {
            'actual_number': '',
            'total_predictions': 0,
            'direct_hits': 0,
            'group_hits': 0,
            'position_hits': {'hundreds': 0, 'tens': 0, 'units': 0},
            'direct_accuracy': 0,
            'group_accuracy': 0,
            'position_accuracy': {'hundreds': 0, 'tens': 0, 'units': 0},
            'overall_accuracy': 0,
            'comparison_time': datetime.now().isoformat()
        }

    def _analyze_trend(self, accuracies: List[float]) -> str:
        """分析准确率趋势"""
        if len(accuracies) < 3:
            return 'insufficient_data'

        recent = accuracies[-3:]
        if recent[-1] > recent[0]:
            return 'improving'
        elif recent[-1] < recent[0]:
            return 'declining'
        else:
            return 'stable'

    def _generate_improvement_suggestions(self, results: List[Dict[str, Any]]) -> List[str]:
        """生成改进建议"""
        suggestions = []

        if not results:
            return suggestions

        # 分析最近的结果
        recent_results = results[-5:] if len(results) >= 5 else results
        avg_accuracy = sum(r.get('overall_accuracy', 0) for r in recent_results) / len(recent_results)

        if avg_accuracy < 0.1:
            suggestions.append("准确率偏低，建议调整预测模型参数")
        elif avg_accuracy < 0.2:
            suggestions.append("建议增加特征维度，提升预测精度")

        # 分析位置准确率
        position_accuracies = {
            'hundreds': sum(r.get('position_accuracy', {}).get('hundreds', 0) for r in recent_results) / len(recent_results),
            'tens': sum(r.get('position_accuracy', {}).get('tens', 0) for r in recent_results) / len(recent_results),
            'units': sum(r.get('position_accuracy', {}).get('units', 0) for r in recent_results) / len(recent_results)
        }

        min_position = min(position_accuracies, key=position_accuracies.get)
        if position_accuracies[min_position] < 0.15:
            suggestions.append(f"建议优化{min_position}位预测器")

        return suggestions