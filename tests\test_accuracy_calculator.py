"""
准确率计算器单元测试
测试 src.analysis.accuracy_calculator 模块的功能

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import unittest
import sys
sys.path.append('.')

from src.analysis.accuracy_calculator import AccuracyCalculator


class TestAccuracyCalculator(unittest.TestCase):
    """准确率计算器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.calculator = AccuracyCalculator()
        self.test_predictions = ['123', '124', '125', '321', '456', '789']
        self.test_actual = '123'
    
    def test_direct_accuracy_perfect_match(self):
        """测试直选准确率 - 完全匹配"""
        predictions = ['123', '123', '123']
        actual = '123'
        
        result = self.calculator.direct_accuracy(predictions, actual)
        
        self.assertEqual(result['type'], 'direct')
        self.assertEqual(result['total_predictions'], 3)
        self.assertEqual(result['hits'], 3)
        self.assertEqual(result['accuracy'], 1.0)
        self.assertEqual(result['percentage'], 100.0)
        self.assertEqual(len(result['hit_predictions']), 3)
    
    def test_direct_accuracy_partial_match(self):
        """测试直选准确率 - 部分匹配"""
        result = self.calculator.direct_accuracy(self.test_predictions, self.test_actual)
        
        self.assertEqual(result['type'], 'direct')
        self.assertEqual(result['total_predictions'], 6)
        self.assertEqual(result['hits'], 1)  # 只有'123'匹配
        self.assertAlmostEqual(result['accuracy'], 1/6, places=3)
        self.assertEqual(len(result['hit_predictions']), 1)
        self.assertEqual(result['hit_predictions'][0], '123')
    
    def test_direct_accuracy_no_match(self):
        """测试直选准确率 - 无匹配"""
        predictions = ['456', '789', '012']
        actual = '123'
        
        result = self.calculator.direct_accuracy(predictions, actual)
        
        self.assertEqual(result['hits'], 0)
        self.assertEqual(result['accuracy'], 0.0)
        self.assertEqual(len(result['hit_predictions']), 0)
    
    def test_group_accuracy_with_permutations(self):
        """测试组选准确率 - 包含排列组合"""
        predictions = ['123', '132', '213', '231', '312', '321', '456']
        actual = '123'
        
        result = self.calculator.group_accuracy(predictions, actual)
        
        self.assertEqual(result['type'], 'group')
        self.assertEqual(result['total_predictions'], 7)
        self.assertEqual(result['hits'], 6)  # 除了'456'，其他都是'123'的排列
        self.assertAlmostEqual(result['accuracy'], 6/7, places=3)
        self.assertEqual(result['actual_sorted'], '123')
    
    def test_group_accuracy_same_digits(self):
        """测试组选准确率 - 相同数字"""
        predictions = ['111', '222', '333']
        actual = '111'
        
        result = self.calculator.group_accuracy(predictions, actual)
        
        self.assertEqual(result['hits'], 1)  # 只有'111'匹配
        self.assertAlmostEqual(result['accuracy'], 1/3, places=3)
    
    def test_position_accuracy_all_positions(self):
        """测试位置准确率 - 各位置"""
        predictions = ['123', '124', '125', '023', '133', '120']
        actual = '123'
        
        result = self.calculator.position_accuracy(predictions, actual)
        
        self.assertEqual(result['type'], 'position')
        self.assertEqual(result['total_predictions'], 6)
        
        # 百位命中：123, 124, 125 (3个)
        self.assertEqual(result['hits']['hundreds'], 3)
        self.assertAlmostEqual(result['accuracy']['hundreds'], 3/6, places=3)
        
        # 十位命中：123, 124, 125, 120 (4个)
        self.assertEqual(result['hits']['tens'], 4)
        self.assertAlmostEqual(result['accuracy']['tens'], 4/6, places=3)
        
        # 个位命中：123, 023, 133 (3个)
        self.assertEqual(result['hits']['units'], 3)
        self.assertAlmostEqual(result['accuracy']['units'], 3/6, places=3)
    
    def test_sum_accuracy_matching_sums(self):
        """测试和值准确率 - 匹配和值"""
        predictions = ['123', '132', '456', '789']  # 和值分别为6, 6, 15, 24
        actual = '321'  # 和值为6
        
        result = self.calculator.sum_accuracy(predictions, actual)
        
        self.assertEqual(result['type'], 'sum')
        self.assertEqual(result['total_predictions'], 4)
        self.assertEqual(result['hits'], 2)  # '123'和'132'的和值都是6
        self.assertEqual(result['actual_sum'], 6)
        self.assertAlmostEqual(result['accuracy'], 2/4, places=3)
    
    def test_span_accuracy_matching_spans(self):
        """测试跨度准确率 - 匹配跨度"""
        predictions = ['123', '135', '246', '789']  # 跨度分别为2, 4, 4, 8
        actual = '135'  # 跨度为4
        
        result = self.calculator.span_accuracy(predictions, actual)
        
        self.assertEqual(result['type'], 'span')
        self.assertEqual(result['total_predictions'], 4)
        self.assertEqual(result['hits'], 2)  # '135'和'246'的跨度都是4
        self.assertEqual(result['actual_span'], 4)
        self.assertAlmostEqual(result['accuracy'], 2/4, places=3)
    
    def test_span_accuracy_zero_span(self):
        """测试跨度准确率 - 零跨度"""
        predictions = ['111', '222', '333', '123']
        actual = '555'  # 跨度为0
        
        result = self.calculator.span_accuracy(predictions, actual)
        
        self.assertEqual(result['actual_span'], 0)
        self.assertEqual(result['hits'], 0)  # 没有跨度为0的预测
    
    def test_comprehensive_accuracy_weighted_calculation(self):
        """测试综合准确率 - 加权计算"""
        predictions = ['123', '124', '125', '321']
        actual = '123'
        
        result = self.calculator.comprehensive_accuracy(predictions, actual)
        
        self.assertEqual(result['type'], 'comprehensive')
        self.assertEqual(result['total_predictions'], 4)
        self.assertIn('dimensions', result)
        self.assertIn('overall_accuracy', result)
        self.assertIn('weights', result)
        
        # 验证各维度结果存在
        dimensions = result['dimensions']
        self.assertIn('direct', dimensions)
        self.assertIn('group', dimensions)
        self.assertIn('position', dimensions)
        self.assertIn('sum', dimensions)
        self.assertIn('span', dimensions)
        
        # 验证权重配置
        weights = result['weights']
        self.assertEqual(weights['direct'], 0.4)
        self.assertEqual(weights['group'], 0.25)
        self.assertEqual(weights['position'], 0.2)
        self.assertEqual(weights['sum'], 0.1)
        self.assertEqual(weights['span'], 0.05)
        
        # 验证综合准确率在合理范围内
        self.assertGreaterEqual(result['overall_accuracy'], 0)
        self.assertLessEqual(result['overall_accuracy'], 1)
    
    def test_empty_predictions_handling(self):
        """测试空预测列表的处理"""
        result = self.calculator.direct_accuracy([], '123')
        
        self.assertEqual(result['total_predictions'], 0)
        self.assertEqual(result['hits'], 0)
        self.assertEqual(result['accuracy'], 0)
        self.assertEqual(len(result['hit_predictions']), 0)
    
    def test_empty_actual_handling(self):
        """测试空实际号码的处理"""
        result = self.calculator.group_accuracy(['123', '456'], '')
        
        self.assertEqual(result['total_predictions'], 0)
        self.assertEqual(result['hits'], 0)
        self.assertEqual(result['accuracy'], 0)
    
    def test_invalid_format_handling(self):
        """测试无效格式的处理"""
        predictions = ['12', '1234', 'abc', '123']  # 包含无效格式
        actual = '123'
        
        result = self.calculator.direct_accuracy(predictions, actual)
        
        # 应该只处理有效的3位数字
        self.assertGreater(result['total_predictions'], 0)
        self.assertGreaterEqual(result['hits'], 1)
    
    def test_number_padding(self):
        """测试数字补零处理"""
        predictions = ['1', '12', '123']  # 测试自动补零
        actual = '001'
        
        result = self.calculator.direct_accuracy(predictions, actual)
        
        # '1'应该被处理为'001'，从而匹配actual
        self.assertEqual(result['hits'], 1)
        self.assertEqual(result['hit_predictions'][0], '1')
    
    def test_all_accuracy_types_consistency(self):
        """测试所有准确率类型的一致性"""
        predictions = ['123', '124', '125']
        actual = '123'
        
        # 测试所有单独的准确率计算
        direct = self.calculator.direct_accuracy(predictions, actual)
        group = self.calculator.group_accuracy(predictions, actual)
        position = self.calculator.position_accuracy(predictions, actual)
        sum_acc = self.calculator.sum_accuracy(predictions, actual)
        span = self.calculator.span_accuracy(predictions, actual)
        comprehensive = self.calculator.comprehensive_accuracy(predictions, actual)
        
        # 验证所有结果都有相同的预测总数
        self.assertEqual(direct['total_predictions'], 3)
        self.assertEqual(group['total_predictions'], 3)
        self.assertEqual(position['total_predictions'], 3)
        self.assertEqual(sum_acc['total_predictions'], 3)
        self.assertEqual(span['total_predictions'], 3)
        self.assertEqual(comprehensive['total_predictions'], 3)
        
        # 验证综合结果包含所有维度
        comp_dims = comprehensive['dimensions']
        self.assertEqual(comp_dims['direct']['hits'], direct['hits'])
        self.assertEqual(comp_dims['group']['hits'], group['hits'])
        self.assertEqual(comp_dims['sum']['hits'], sum_acc['hits'])
        self.assertEqual(comp_dims['span']['hits'], span['hits'])


class TestAccuracyCalculatorEdgeCases(unittest.TestCase):
    """准确率计算器边界情况测试"""
    
    def setUp(self):
        """测试前准备"""
        self.calculator = AccuracyCalculator()
    
    def test_large_prediction_list(self):
        """测试大量预测的处理"""
        predictions = [f"{i:03d}" for i in range(1000)]  # 000-999
        actual = '500'
        
        result = self.calculator.direct_accuracy(predictions, actual)
        
        self.assertEqual(result['total_predictions'], 1000)
        self.assertEqual(result['hits'], 1)
        self.assertEqual(result['accuracy'], 0.001)
    
    def test_duplicate_predictions(self):
        """测试重复预测的处理"""
        predictions = ['123', '123', '123', '456']
        actual = '123'
        
        result = self.calculator.direct_accuracy(predictions, actual)
        
        self.assertEqual(result['total_predictions'], 4)
        self.assertEqual(result['hits'], 3)  # 所有重复的'123'都算命中
        self.assertEqual(len(result['hit_predictions']), 3)


if __name__ == '__main__':
    unittest.main()
