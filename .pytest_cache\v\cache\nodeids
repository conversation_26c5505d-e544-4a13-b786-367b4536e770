["tests/test_p9_basic.py::TestP9Basic::test_p9_component_imports", "tests/test_p9_basic.py::TestP9Basic::test_p9_manager_initialization", "tests/test_p9_basic.py::TestP9Basic::test_p9_optimizer_initialization", "tests/test_p9_basic.py::TestP9Basic::test_p9_system_lifecycle", "tests/test_p9_basic.py::TestP9Basic::test_p9_system_status", "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_empty_results", "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_with_data", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_direct_hit", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_empty_input", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_group_hit", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_invalid_format", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_position_hits", "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary", "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary_empty_data", "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions", "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions_nonexistent_issue", "tests/test_review_engine.py::TestReviewEngine::test_lottery_query_integration", "tests/test_review_engine.py::TestReviewEngineIntegration::test_full_review_workflow"]