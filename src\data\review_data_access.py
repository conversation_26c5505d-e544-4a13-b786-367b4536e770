"""
复盘数据访问层
提供复盘结果的存储、查询和管理功能

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import sqlite3
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# 配置日志
logger = logging.getLogger(__name__)


class ReviewDataAccess:
    """复盘数据访问类"""
    
    def __init__(self, db_path: str = "data/fusion_predictions.db"):
        """
        初始化复盘数据访问层
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self._init_tables()
        
    def _init_tables(self):
        """初始化复盘相关数据表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 创建复盘结果表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS review_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        issue TEXT NOT NULL,
                        review_date TEXT NOT NULL,
                        actual_number TEXT NOT NULL,
                        predicted_numbers TEXT NOT NULL,  -- JSON格式存储预测号码列表
                        total_predictions INTEGER NOT NULL,
                        direct_hits INTEGER NOT NULL,
                        group_hits INTEGER NOT NULL,
                        position_hits TEXT NOT NULL,  -- JSON格式存储位置命中数据
                        direct_accuracy REAL NOT NULL,
                        group_accuracy REAL NOT NULL,
                        position_accuracy TEXT NOT NULL,  -- JSON格式存储位置准确率
                        overall_accuracy REAL NOT NULL,
                        comparison_details TEXT,  -- JSON格式存储详细对比结果
                        improvement_suggestions TEXT,  -- JSON格式存储改进建议
                        summary TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(issue, review_date)
                    )
                """)
                
                # 创建复盘统计表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS review_statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        period_start TEXT NOT NULL,
                        period_end TEXT NOT NULL,
                        total_reviews INTEGER NOT NULL,
                        average_accuracy REAL NOT NULL,
                        best_accuracy REAL NOT NULL,
                        worst_accuracy REAL NOT NULL,
                        trend TEXT,
                        statistics_data TEXT,  -- JSON格式存储详细统计数据
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(period_start, period_end)
                    )
                """)
                
                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_review_issue ON review_results(issue)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_review_date ON review_results(review_date)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_review_accuracy ON review_results(overall_accuracy)")
                
                conn.commit()
                logger.info("复盘数据表初始化完成")
                
        except Exception as e:
            logger.error(f"初始化复盘数据表失败: {e}")
            raise
    
    def save_review_result(self, review_data: Dict[str, Any]) -> bool:
        """
        保存复盘结果
        
        Args:
            review_data: 复盘结果数据
            
        Returns:
            是否保存成功
        """
        try:
            # 提取必要字段
            issue = review_data.get('issue', '')
            actual_number = review_data.get('actual_number', '')
            predicted_numbers = review_data.get('predicted_numbers', [])
            comparison_details = review_data.get('comparison_details', {})
            
            if not issue or not actual_number:
                logger.warning("复盘数据缺少必要字段")
                return False
            
            # 从comparison_details中提取数据
            total_predictions = comparison_details.get('total_predictions', 0)
            direct_hits = comparison_details.get('direct_hits', 0)
            group_hits = comparison_details.get('group_hits', 0)
            position_hits = comparison_details.get('position_hits', {})
            direct_accuracy = comparison_details.get('direct_accuracy', 0)
            group_accuracy = comparison_details.get('group_accuracy', 0)
            position_accuracy = comparison_details.get('position_accuracy', {})
            overall_accuracy = comparison_details.get('overall_accuracy', 0)
            
            with sqlite3.connect(self.db_path) as conn:
                sql = """
                    INSERT OR REPLACE INTO review_results 
                    (issue, review_date, actual_number, predicted_numbers, total_predictions,
                     direct_hits, group_hits, position_hits, direct_accuracy, group_accuracy,
                     position_accuracy, overall_accuracy, comparison_details, 
                     improvement_suggestions, summary)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                conn.execute(sql, (
                    issue,
                    datetime.now().strftime('%Y-%m-%d'),
                    actual_number,
                    json.dumps(predicted_numbers, ensure_ascii=False),
                    total_predictions,
                    direct_hits,
                    group_hits,
                    json.dumps(position_hits, ensure_ascii=False),
                    direct_accuracy,
                    group_accuracy,
                    json.dumps(position_accuracy, ensure_ascii=False),
                    overall_accuracy,
                    json.dumps(comparison_details, ensure_ascii=False),
                    json.dumps(review_data.get('improvement_suggestions', []), ensure_ascii=False),
                    review_data.get('summary', '')
                ))
                
                conn.commit()
                logger.info(f"复盘结果保存成功: 期号{issue}, 准确率{overall_accuracy:.2%}")
                return True
                
        except Exception as e:
            logger.error(f"保存复盘结果失败: {e}")
            return False
    
    def get_review_by_issue(self, issue: str) -> Optional[Dict[str, Any]]:
        """
        根据期号获取复盘结果
        
        Args:
            issue: 期号
            
        Returns:
            复盘结果数据
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM review_results WHERE issue = ? ORDER BY created_at DESC LIMIT 1",
                    (issue,)
                )
                
                result = cursor.fetchone()
                if result:
                    return self._format_review_result(dict(result))
                return None
                
        except Exception as e:
            logger.error(f"查询复盘结果失败: {e}")
            return None
    
    def get_recent_reviews(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的复盘结果
        
        Args:
            limit: 获取数量限制
            
        Returns:
            复盘结果列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    """
                    SELECT * FROM review_results 
                    ORDER BY created_at DESC 
                    LIMIT ?
                    """,
                    (limit,)
                )
                
                results = cursor.fetchall()
                return [self._format_review_result(dict(row)) for row in results]
                
        except Exception as e:
            logger.error(f"查询最近复盘结果失败: {e}")
            return []
    
    def get_reviews_by_date_range(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        根据日期范围获取复盘结果
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            复盘结果列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    """
                    SELECT * FROM review_results 
                    WHERE review_date BETWEEN ? AND ?
                    ORDER BY review_date DESC, created_at DESC
                    """,
                    (start_date, end_date)
                )
                
                results = cursor.fetchall()
                return [self._format_review_result(dict(row)) for row in results]
                
        except Exception as e:
            logger.error(f"按日期范围查询复盘结果失败: {e}")
            return []
    
    def get_accuracy_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        获取准确率统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            统计信息
        """
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    """
                    SELECT 
                        COUNT(*) as total_reviews,
                        AVG(overall_accuracy) as avg_accuracy,
                        MAX(overall_accuracy) as max_accuracy,
                        MIN(overall_accuracy) as min_accuracy,
                        AVG(direct_accuracy) as avg_direct_accuracy,
                        AVG(group_accuracy) as avg_group_accuracy
                    FROM review_results 
                    WHERE review_date BETWEEN ? AND ?
                    """,
                    (start_date, end_date)
                )
                
                result = cursor.fetchone()
                if result:
                    return {
                        'period': f"{start_date} 到 {end_date}",
                        'total_reviews': result[0] or 0,
                        'average_accuracy': result[1] or 0,
                        'best_accuracy': result[2] or 0,
                        'worst_accuracy': result[3] or 0,
                        'average_direct_accuracy': result[4] or 0,
                        'average_group_accuracy': result[5] or 0
                    }
                
                return {
                    'period': f"{start_date} 到 {end_date}",
                    'total_reviews': 0,
                    'average_accuracy': 0,
                    'best_accuracy': 0,
                    'worst_accuracy': 0,
                    'average_direct_accuracy': 0,
                    'average_group_accuracy': 0
                }
                
        except Exception as e:
            logger.error(f"获取准确率统计失败: {e}")
            return {}
    
    def delete_old_reviews(self, days: int = 90) -> int:
        """
        删除过期的复盘记录
        
        Args:
            days: 保留天数
            
        Returns:
            删除的记录数
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "DELETE FROM review_results WHERE review_date < ?",
                    (cutoff_date,)
                )
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"删除了 {deleted_count} 条过期复盘记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"删除过期复盘记录失败: {e}")
            return 0
    
    def _format_review_result(self, row: Dict[str, Any]) -> Dict[str, Any]:
        """格式化复盘结果数据"""
        try:
            # 解析JSON字段
            predicted_numbers = json.loads(row.get('predicted_numbers', '[]'))
            position_hits = json.loads(row.get('position_hits', '{}'))
            position_accuracy = json.loads(row.get('position_accuracy', '{}'))
            comparison_details = json.loads(row.get('comparison_details', '{}'))
            improvement_suggestions = json.loads(row.get('improvement_suggestions', '[]'))
            
            return {
                'id': row.get('id'),
                'issue': row.get('issue'),
                'review_date': row.get('review_date'),
                'actual_number': row.get('actual_number'),
                'predicted_numbers': predicted_numbers,
                'total_predictions': row.get('total_predictions'),
                'direct_hits': row.get('direct_hits'),
                'group_hits': row.get('group_hits'),
                'position_hits': position_hits,
                'direct_accuracy': row.get('direct_accuracy'),
                'group_accuracy': row.get('group_accuracy'),
                'position_accuracy': position_accuracy,
                'overall_accuracy': row.get('overall_accuracy'),
                'comparison_details': comparison_details,
                'improvement_suggestions': improvement_suggestions,
                'summary': row.get('summary'),
                'created_at': row.get('created_at')
            }
            
        except Exception as e:
            logger.error(f"格式化复盘结果失败: {e}")
            return row
