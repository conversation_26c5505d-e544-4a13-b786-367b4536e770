"""
复盘引擎单元测试
测试 src.analysis.review_engine 模块的功能

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import unittest
import tempfile
import os
import sqlite3
from unittest.mock import patch, MagicMock
import sys
sys.path.append('.')

from src.analysis.review_engine import ReviewEngine


class TestReviewEngine(unittest.TestCase):
    """复盘引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化测试数据库
        self._init_test_db()
        
        # 创建复盘引擎实例
        self.engine = ReviewEngine(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _init_test_db(self):
        """初始化测试数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建测试表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fusion_predictions (
                    id INTEGER PRIMARY KEY,
                    issue TEXT,
                    combination TEXT,
                    rank INTEGER,
                    timestamp TEXT
                )
            """)
            
            # 插入测试数据
            test_predictions = [
                ('2025001', '123', 1, '2025-01-01 10:00:00'),
                ('2025001', '124', 2, '2025-01-01 10:00:00'),
                ('2025001', '125', 3, '2025-01-01 10:00:00'),
                ('2025001', '321', 4, '2025-01-01 10:00:00'),
                ('2025001', '456', 5, '2025-01-01 10:00:00'),
            ]
            
            conn.executemany(
                "INSERT INTO fusion_predictions (issue, combination, rank, timestamp) VALUES (?, ?, ?, ?)",
                test_predictions
            )
            conn.commit()
    
    def test_compare_predictions_direct_hit(self):
        """测试直选命中的预测对比"""
        predictions = ['123', '124', '125', '321', '456']
        actual = '123'
        
        result = self.engine.compare_predictions(predictions, actual)
        
        self.assertEqual(result['actual_number'], '123')
        self.assertEqual(result['total_predictions'], 5)
        self.assertEqual(result['direct_hits'], 1)
        self.assertEqual(result['direct_accuracy'], 0.2)
        self.assertGreater(result['overall_accuracy'], 0)
    
    def test_compare_predictions_group_hit(self):
        """测试组选命中的预测对比"""
        predictions = ['123', '124', '125', '321', '456']
        actual = '321'  # 321和123是组选关系
        
        result = self.engine.compare_predictions(predictions, actual)
        
        self.assertEqual(result['actual_number'], '321')
        self.assertEqual(result['group_hits'], 2)  # 123和321都是组选命中
        self.assertGreater(result['group_accuracy'], 0)
    
    def test_compare_predictions_position_hits(self):
        """测试位置命中的预测对比"""
        predictions = ['123', '124', '125', '321', '456']
        actual = '129'  # 百位1命中3个，十位2命中2个，个位9命中0个
        
        result = self.engine.compare_predictions(predictions, actual)
        
        position_hits = result['position_hits']
        self.assertEqual(position_hits['hundreds'], 3)  # 123, 124, 125的百位都是1
        self.assertEqual(position_hits['tens'], 2)      # 123, 124的十位都是2
        self.assertEqual(position_hits['units'], 0)     # 没有个位9
    
    def test_compare_predictions_empty_input(self):
        """测试空输入的处理"""
        # 测试空预测列表
        result = self.engine.compare_predictions([], '123')
        self.assertEqual(result['total_predictions'], 0)
        self.assertEqual(result['overall_accuracy'], 0)
        
        # 测试空实际号码
        result = self.engine.compare_predictions(['123'], '')
        self.assertEqual(result['total_predictions'], 0)
        self.assertEqual(result['overall_accuracy'], 0)
    
    def test_compare_predictions_invalid_format(self):
        """测试无效格式的处理"""
        predictions = ['12', '1234', 'abc', '123']  # 包含无效格式
        actual = '123'
        
        result = self.engine.compare_predictions(predictions, actual)
        
        # 应该只处理有效的3位数字
        self.assertGreater(result['total_predictions'], 0)
        self.assertGreaterEqual(result['direct_hits'], 1)
    
    def test_generate_review_summary(self):
        """测试复盘摘要生成"""
        review_data = {
            'actual_number': '123',
            'total_predictions': 5,
            'direct_hits': 1,
            'group_hits': 2,
            'position_hits': {'hundreds': 3, 'tens': 2, 'units': 1},
            'overall_accuracy': 0.35
        }
        
        summary = self.engine.generate_review_summary(review_data)
        
        self.assertIn('实际开奖: 123', summary)
        self.assertIn('预测总数: 5', summary)
        self.assertIn('直选命中: 1/5', summary)
        self.assertIn('组选命中: 2/5', summary)
        self.assertIn('综合准确率: 35.00%', summary)
    
    def test_generate_review_summary_empty_data(self):
        """测试空数据的摘要生成"""
        summary = self.engine.generate_review_summary({})
        self.assertIn('复盘数据为空', summary)
    
    def test_get_recent_predictions(self):
        """测试获取最近预测结果"""
        predictions = self.engine.get_recent_predictions('2025001', limit=3)
        
        self.assertEqual(len(predictions), 3)
        self.assertEqual(predictions[0], '123')  # 按rank排序，第一个应该是rank=1的
        self.assertEqual(predictions[1], '124')
        self.assertEqual(predictions[2], '125')
    
    def test_get_recent_predictions_nonexistent_issue(self):
        """测试获取不存在期号的预测结果"""
        predictions = self.engine.get_recent_predictions('2025999', limit=10)
        self.assertEqual(len(predictions), 0)
    
    @patch('src.analysis.review_engine.ReviewEngine.get_recent_predictions')
    def test_analyze_prediction_quality_empty_results(self, mock_get_predictions):
        """测试空结果的预测质量分析"""
        mock_get_predictions.return_value = []
        
        analysis = self.engine.analyze_prediction_quality([])
        
        self.assertEqual(analysis['total_periods'], 0)
        self.assertEqual(analysis['average_accuracy'], 0)
        self.assertEqual(analysis['trend'], 'stable')
    
    def test_analyze_prediction_quality_with_data(self):
        """测试有数据的预测质量分析"""
        results = [
            {'overall_accuracy': 0.3},
            {'overall_accuracy': 0.4},
            {'overall_accuracy': 0.5},
        ]
        
        analysis = self.engine.analyze_prediction_quality(results)
        
        self.assertEqual(analysis['total_periods'], 3)
        self.assertAlmostEqual(analysis['average_accuracy'], 0.4, places=2)
        self.assertEqual(analysis['best_accuracy'], 0.5)
        self.assertEqual(analysis['worst_accuracy'], 0.3)
        self.assertEqual(analysis['trend'], 'improving')
    
    @patch('src.data.lottery_query.LotteryQueryEngine.get_result_by_issue')
    def test_lottery_query_integration(self, mock_get_result):
        """测试与开奖查询引擎的集成"""
        # 模拟开奖查询返回
        mock_get_result.return_value = {
            'issue': '2025001',
            'numbers': '123',
            'draw_date': '2025-01-01'
        }
        
        # 测试查询功能
        result = self.engine.lottery_query.get_result_by_issue('2025001')
        self.assertIsNotNone(result)
        self.assertEqual(result['numbers'], '123')


class TestReviewEngineIntegration(unittest.TestCase):
    """复盘引擎集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        self.engine = ReviewEngine(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_full_review_workflow(self):
        """测试完整的复盘工作流程"""
        # 1. 准备测试数据
        predictions = ['123', '124', '125', '321', '456']
        actual = '123'
        
        # 2. 执行预测对比
        comparison_result = self.engine.compare_predictions(predictions, actual)
        
        # 3. 生成摘要
        summary = self.engine.generate_review_summary(comparison_result)
        
        # 4. 分析质量
        analysis = self.engine.analyze_prediction_quality([comparison_result])
        
        # 5. 验证结果
        self.assertIsNotNone(comparison_result)
        self.assertIsNotNone(summary)
        self.assertIsNotNone(analysis)
        self.assertGreater(len(summary), 0)
        self.assertEqual(analysis['total_periods'], 1)


if __name__ == '__main__':
    unittest.main()
