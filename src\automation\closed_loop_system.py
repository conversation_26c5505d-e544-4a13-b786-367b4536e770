#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
福彩3D闭环自动化系统
实现真正的闭环：数据更新 -> 预测 -> 复盘 -> 优化 -> 迭代
"""

import os
import sys
import time
import sqlite3
import requests
import schedule
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.data.updater import smart_incremental_update
from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
from src.fusion.fusion_predictor import FusionPredictor
from src.optimization.intelligent_closed_loop_optimizer import IntelligentClosedLoopOptimizer
from src.analysis.review_engine import ReviewEngine
from src.data.review_data_access import ReviewDataAccess

class ClosedLoopSystem:
    """福彩3D闭环自动化系统"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.db_path = "data/lottery.db"
        self.fusion_db_path = "data/fucai3d.db"
        
        # 初始化核心组件
        self.predictor_interface = UnifiedPredictorInterface(self.db_path)
        self.fusion_predictor = FusionPredictor(self.fusion_db_path)
        self.optimizer = IntelligentClosedLoopOptimizer(self.fusion_db_path)
        self.review_engine = ReviewEngine(self.fusion_db_path)
        self.review_data_access = ReviewDataAccess(self.fusion_db_path)
        
        # 闭环配置
        self.config = {
            'data_update_time': "21:35",  # 每天21:35更新数据
            'prediction_time': "21:40",   # 21:40进行预测
            'review_time': "22:00",       # 22:00进行复盘
            'optimization_time': "02:00", # 凌晨2:00进行优化
            'max_retries': 3,
            'retry_delay': 300,  # 5分钟
        }
        
        self.logger.info("🚀 福彩3D闭环自动化系统初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('ClosedLoopSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def auto_data_update(self) -> bool:
        """自动数据更新"""
        self.logger.info("🔄 开始自动数据更新...")
        
        try:
            # 使用智能增量更新
            success = smart_incremental_update()
            
            if success:
                self.logger.info("✅ 数据更新检查完成")

                # 检查是否有新数据
                new_count = self._check_new_data()
                if new_count > 0:
                    self.logger.info(f"📊 发现 {new_count} 条新数据，触发预测流程")
                    # 延迟5分钟后进行预测
                    schedule.every().minute.do(self.auto_prediction).tag('one_time')
                    return True
                else:
                    self.logger.info("📊 数据库已是最新状态，等待下次更新")
                    return True
            else:
                self.logger.warning("⚠️ 数据更新检查遇到问题，将在下次定时任务重试")
                return True  # 改为返回True，避免影响系统运行
                
        except Exception as e:
            self.logger.error(f"❌ 数据更新异常: {e}")
            return False
    
    def _check_new_data(self) -> int:
        """检查新数据数量"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查今天是否有新数据
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute(
                "SELECT COUNT(*) FROM lottery_data WHERE draw_date = ?",
                (today,)
            )
            count = cursor.fetchone()[0]
            conn.close()
            
            return count
            
        except Exception as e:
            self.logger.error(f"检查新数据失败: {e}")
            return 0
    
    def auto_prediction(self) -> bool:
        """自动预测"""
        self.logger.info("🎯 开始自动预测...")
        
        try:
            # 获取下一期期号
            next_issue = self._get_next_issue()
            if not next_issue:
                self.logger.error("❌ 无法获取下一期期号")
                return False
            
            self.logger.info(f"🎲 预测期号: {next_issue}")
            
            # 执行融合预测
            prediction_result = self.fusion_predictor.predict_next_period(next_issue)
            
            if prediction_result and prediction_result.get('success'):
                self.logger.info("✅ 预测完成")
                self.logger.info(f"🎯 预测结果: {prediction_result.get('final_prediction')}")
                
                # 保存预测记录
                self._save_prediction_record(next_issue, prediction_result)
                return True
            else:
                self.logger.error("❌ 预测失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 预测异常: {e}")
            return False
        finally:
            # 清理一次性任务
            schedule.clear('one_time')
    
    def _get_next_issue(self) -> Optional[str]:
        """获取下一期期号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT issue FROM lottery_data ORDER BY issue DESC LIMIT 1"
            )
            result = cursor.fetchone()
            conn.close()
            
            if result:
                last_issue = result[0]
                # 简单的期号递增逻辑
                next_issue_num = int(last_issue) + 1
                return str(next_issue_num)
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一期期号失败: {e}")
            return None
    
    def _save_prediction_record(self, issue: str, prediction_result: Dict[str, Any]):
        """保存预测记录"""
        try:
            conn = sqlite3.connect(self.fusion_db_path)
            cursor = conn.cursor()
            
            # 保存到预测记录表
            cursor.execute("""
                INSERT OR REPLACE INTO prediction_records (
                    issue, prediction_time, prediction_data, model_version
                ) VALUES (?, ?, ?, ?)
            """, (
                issue,
                datetime.now().isoformat(),
                json.dumps(prediction_result),
                "v1.0"
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"📝 预测记录已保存: {issue}")
            
        except Exception as e:
            self.logger.error(f"保存预测记录失败: {e}")
    
    def auto_review(self) -> bool:
        """自动复盘"""
        self.logger.info("📊 开始自动复盘...")

        try:
            # 获取最近的预测和实际结果
            review_results = self._perform_review()

            if review_results:
                # 详细的复盘日志输出
                self._log_detailed_review_results(review_results)

                # 如果准确率低于阈值，触发优化
                accuracy = review_results.get('accuracy', 0)
                if accuracy < 0.6:
                    self.logger.warning("⚠️ 准确率偏低，触发优化流程")
                    schedule.every().minute.do(self.auto_optimization).tag('one_time')

                return True
            else:
                self.logger.warning("⚠️ 复盘数据不足")
                return False

        except Exception as e:
            self.logger.error(f"❌ 复盘异常: {e}")
            return False
    
    def _perform_review(self) -> Optional[Dict[str, Any]]:
        """执行复盘分析"""
        try:
            # 获取最新的预测期号和实际开奖结果
            latest_prediction = self._get_latest_prediction()
            if not latest_prediction:
                self.logger.warning("未找到最新预测记录")
                return None

            issue = latest_prediction['issue']

            # 获取实际开奖结果
            actual_result = self.review_engine.lottery_query.get_result_by_issue(issue)
            if not actual_result:
                self.logger.warning(f"未找到期号 {issue} 的开奖结果")
                return None

            # 获取预测号码列表
            predictions = self.review_engine.get_recent_predictions(issue, limit=20)
            if not predictions:
                self.logger.warning(f"未找到期号 {issue} 的预测结果")
                return None

            actual_number = actual_result['numbers']

            # 执行真实复盘对比
            comparison_result = self.review_engine.compare_predictions(predictions, actual_number)

            # 生成复盘摘要
            summary = self.review_engine.generate_review_summary(comparison_result)

            # 格式化返回结果（保持与原接口兼容）
            review_results = {
                'issue': issue,
                'accuracy': comparison_result.get('overall_accuracy', 0),
                'reviewed_count': comparison_result.get('total_predictions', 0),
                'correct_predictions': comparison_result.get('direct_hits', 0),
                'actual_number': actual_number,
                'predicted_numbers': predictions,
                'comparison_details': comparison_result,
                'summary': summary,
                'improvement_suggestions': self._generate_suggestions(comparison_result)
            }

            # 保存复盘结果到数据库
            save_success = self.review_data_access.save_review_result(review_results)
            if save_success:
                self.logger.info(f"复盘结果已保存到数据库")
            else:
                self.logger.warning(f"复盘结果保存失败")

            self.logger.info(f"复盘完成: 期号{issue}, 准确率{comparison_result.get('overall_accuracy', 0):.2%}")
            return review_results

        except Exception as e:
            self.logger.error(f"执行复盘失败: {e}")
            return None

    def _get_latest_prediction(self) -> Optional[Dict[str, Any]]:
        """获取最新的预测记录"""
        try:
            conn = sqlite3.connect(self.fusion_db_path)
            conn.row_factory = sqlite3.Row

            cursor = conn.execute(
                """
                SELECT DISTINCT issue, session_id, timestamp
                FROM fusion_predictions
                ORDER BY timestamp DESC
                LIMIT 1
                """
            )

            result = cursor.fetchone()
            conn.close()

            if result:
                return dict(result)
            return None

        except Exception as e:
            self.logger.error(f"获取最新预测记录失败: {e}")
            return None

    def _generate_suggestions(self, comparison_result: Dict[str, Any]) -> List[str]:
        """根据复盘结果生成改进建议"""
        suggestions = []

        try:
            accuracy = comparison_result.get('overall_accuracy', 0)
            direct_accuracy = comparison_result.get('direct_accuracy', 0)
            position_accuracy = comparison_result.get('position_accuracy', {})

            if accuracy < 0.1:
                suggestions.append("准确率偏低，建议调整预测模型参数")
            elif accuracy < 0.2:
                suggestions.append("建议增加特征维度，提升预测精度")

            if direct_accuracy == 0:
                suggestions.append("直选命中率为0，建议优化融合策略")

            # 检查位置准确率
            if position_accuracy:
                min_position = min(position_accuracy, key=position_accuracy.get)
                if position_accuracy[min_position] < 0.15:
                    position_names = {'hundreds': '百位', 'tens': '十位', 'units': '个位'}
                    suggestions.append(f"建议优化{position_names.get(min_position, min_position)}预测器")

            if not suggestions:
                suggestions.append("预测表现良好，继续保持当前策略")

        except Exception as e:
            self.logger.error(f"生成改进建议失败: {e}")
            suggestions.append("建议进行全面的模型评估")

        return suggestions

    def _log_detailed_review_results(self, review_results: Dict[str, Any]) -> None:
        """详细记录复盘结果"""
        try:
            self.logger.info("✅ 复盘完成")
            self.logger.info("=" * 60)

            # 基本信息
            actual_number = review_results.get('actual_number', 'N/A')
            total_predictions = review_results.get('reviewed_count', 0)
            correct_predictions = review_results.get('correct_predictions', 0)
            accuracy = review_results.get('accuracy', 0)

            self.logger.info(f"🎲 实际开奖号码: {actual_number}")
            self.logger.info(f"🎯 预测总数: {total_predictions}")
            self.logger.info(f"✅ 直选命中: {correct_predictions}")
            self.logger.info(f"📈 综合准确率: {accuracy:.2%}")

            # 详细对比结果
            comparison_details = review_results.get('comparison_details', {})
            if comparison_details:
                self.logger.info("-" * 40)
                self.logger.info("📊 详细对比结果:")

                # 各维度命中情况
                direct_hits = comparison_details.get('direct_hits', 0)
                group_hits = comparison_details.get('group_hits', 0)
                position_hits = comparison_details.get('position_hits', {})

                self.logger.info(f"  🎯 直选命中: {direct_hits}/{total_predictions} ({direct_hits/total_predictions*100:.1f}%)")
                self.logger.info(f"  🔄 组选命中: {group_hits}/{total_predictions} ({group_hits/total_predictions*100:.1f}%)")

                if position_hits:
                    h_hits = position_hits.get('hundreds', 0)
                    t_hits = position_hits.get('tens', 0)
                    u_hits = position_hits.get('units', 0)
                    self.logger.info(f"  📍 位置命中:")
                    self.logger.info(f"    百位: {h_hits}/{total_predictions} ({h_hits/total_predictions*100:.1f}%)")
                    self.logger.info(f"    十位: {t_hits}/{total_predictions} ({t_hits/total_predictions*100:.1f}%)")
                    self.logger.info(f"    个位: {u_hits}/{total_predictions} ({u_hits/total_predictions*100:.1f}%)")

            # 预测号码列表
            predicted_numbers = review_results.get('predicted_numbers', [])
            if predicted_numbers:
                self.logger.info("-" * 40)
                self.logger.info(f"🔢 预测号码列表 (前10个): {predicted_numbers[:10]}")
                if len(predicted_numbers) > 10:
                    self.logger.info(f"   ... 还有 {len(predicted_numbers) - 10} 个预测号码")

            # 改进建议
            suggestions = review_results.get('improvement_suggestions', [])
            if suggestions:
                self.logger.info("-" * 40)
                self.logger.info("💡 改进建议:")
                for i, suggestion in enumerate(suggestions, 1):
                    self.logger.info(f"  {i}. {suggestion}")

            # 复盘摘要
            summary = review_results.get('summary', '')
            if summary:
                self.logger.info("-" * 40)
                self.logger.info("📋 复盘摘要:")
                for line in summary.split('\n'):
                    if line.strip():
                        self.logger.info(f"  {line.strip()}")

            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"记录详细复盘结果失败: {e}")
            # 回退到简单日志
            self.logger.info("✅ 复盘完成")
            self.logger.info(f"📈 准确率: {review_results.get('accuracy', 0):.2%}")
    
    def auto_optimization(self) -> bool:
        """自动优化"""
        self.logger.info("⚙️ 开始自动优化...")
        
        try:
            # 执行智能优化
            optimization_result = self.optimizer.run_optimization_cycle()
            
            if optimization_result and optimization_result.get('success'):
                self.logger.info("✅ 优化完成")
                self.logger.info(f"📈 优化效果: {optimization_result.get('improvement_score', 0):.3f}")
                return True
            else:
                self.logger.error("❌ 优化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 优化异常: {e}")
            return False
        finally:
            # 清理一次性任务
            schedule.clear('one_time')
    
    def setup_schedule(self):
        """设置定时任务"""
        self.logger.info("⏰ 设置闭环定时任务...")
        
        # 每日数据更新
        schedule.every().day.at(self.config['data_update_time']).do(self.auto_data_update)
        
        # 每日预测（如果有新数据）
        schedule.every().day.at(self.config['prediction_time']).do(self.auto_prediction)
        
        # 每日复盘
        schedule.every().day.at(self.config['review_time']).do(self.auto_review)
        
        # 每日优化
        schedule.every().day.at(self.config['optimization_time']).do(self.auto_optimization)
        
        self.logger.info("✅ 定时任务设置完成")
        self.logger.info(f"📅 数据更新: {self.config['data_update_time']}")
        self.logger.info(f"🎯 自动预测: {self.config['prediction_time']}")
        self.logger.info(f"📊 自动复盘: {self.config['review_time']}")
        self.logger.info(f"⚙️ 自动优化: {self.config['optimization_time']}")
    
    def run(self):
        """运行闭环系统"""
        self.logger.info("🚀 启动福彩3D闭环自动化系统")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 立即执行一次数据更新检查
        self.logger.info("🔄 执行初始数据更新检查...")
        self.auto_data_update()
        
        # 运行调度器
        self.logger.info("⏰ 开始运行定时调度器...")
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("👋 用户中断，系统退出")
                break
            except Exception as e:
                self.logger.error(f"❌ 调度器异常: {e}")
                time.sleep(60)

if __name__ == "__main__":
    system = ClosedLoopSystem()
    system.run()
