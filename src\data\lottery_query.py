"""
福彩3D开奖号码查询引擎
复用现有updater.py的数据获取机制，提供真实开奖号码查询功能

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import sqlite3
import requests
import re
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# 配置日志
logger = logging.getLogger(__name__)


class LotteryQueryEngine:
    """福彩3D开奖号码查询引擎"""
    
    def __init__(self, db_path: str = "data/lottery.db"):
        """
        初始化查询引擎
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.data_sources = {
            'primary': "https://data.17500.cn/3d_desc.txt",  # 倒序：最新数据在前
            'backup': "https://data.17500.cn/3d_asc.txt"     # 正序：备用数据源
        }
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def get_result_by_issue(self, issue: str) -> Optional[Dict[str, Any]]:
        """
        根据期号查询开奖结果
        
        Args:
            issue: 期号 (如: "2025211")
            
        Returns:
            开奖结果字典，包含期号、日期、开奖号码等信息
        """
        try:
            # 首先从数据库查询
            result = self._query_from_database(issue)
            if result:
                logger.info(f"从数据库获取期号 {issue} 的开奖结果")
                return result
            
            # 数据库中没有，尝试从网络获取
            logger.info(f"数据库中未找到期号 {issue}，尝试从网络获取")
            result = self._query_from_network(issue)
            if result:
                # 保存到数据库
                self._save_to_database(result)
                logger.info(f"从网络获取期号 {issue} 的开奖结果并保存到数据库")
                return result
            
            logger.warning(f"未找到期号 {issue} 的开奖结果")
            return None
            
        except Exception as e:
            logger.error(f"查询期号 {issue} 开奖结果失败: {e}")
            return None
    
    def get_latest_result(self) -> Optional[Dict[str, Any]]:
        """
        获取最新开奖结果
        
        Returns:
            最新开奖结果字典
        """
        try:
            # 首先从数据库获取最新结果
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM lottery_data ORDER BY draw_date DESC, issue DESC LIMIT 1"
                )
                result = cursor.fetchone()
                if result:
                    db_result = dict(result)
                    logger.info(f"从数据库获取最新开奖结果: 期号 {db_result.get('issue')}")
                    return self._format_result(db_result)
            
            # 数据库为空或需要更新，从网络获取
            logger.info("从网络获取最新开奖结果")
            return self._get_latest_from_network()
            
        except Exception as e:
            logger.error(f"获取最新开奖结果失败: {e}")
            return None
    
    def validate_result_data(self, data: Dict[str, Any]) -> bool:
        """
        验证开奖结果数据的有效性
        
        Args:
            data: 开奖结果数据
            
        Returns:
            数据是否有效
        """
        try:
            # 检查必要字段
            required_fields = ['issue', 'draw_date', 'numbers']
            for field in required_fields:
                if field not in data or not data[field]:
                    logger.warning(f"开奖数据缺少必要字段: {field}")
                    return False
            
            # 验证期号格式 (7位数字)
            issue = str(data['issue'])
            if not re.match(r'^\d{7}$', issue):
                logger.warning(f"期号格式无效: {issue}")
                return False
            
            # 验证开奖号码格式 (3位数字)
            numbers = str(data['numbers'])
            if not re.match(r'^\d{3}$', numbers):
                logger.warning(f"开奖号码格式无效: {numbers}")
                return False
            
            # 验证日期格式
            try:
                datetime.strptime(data['draw_date'], '%Y-%m-%d')
            except ValueError:
                logger.warning(f"日期格式无效: {data['draw_date']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证开奖数据失败: {e}")
            return False
    
    def _query_from_database(self, issue: str) -> Optional[Dict[str, Any]]:
        """从数据库查询开奖结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM lottery_data WHERE issue = ?",
                    (issue,)
                )
                result = cursor.fetchone()
                if result:
                    return self._format_result(dict(result))
                return None
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            return None
    
    def _query_from_network(self, issue: str) -> Optional[Dict[str, Any]]:
        """从网络查询开奖结果"""
        try:
            # 尝试主要数据源
            result = self._fetch_from_source(self.data_sources['primary'], issue)
            if result:
                return result
            
            # 尝试备用数据源
            logger.info("主要数据源查询失败，尝试备用数据源")
            result = self._fetch_from_source(self.data_sources['backup'], issue)
            return result
            
        except Exception as e:
            logger.error(f"网络查询失败: {e}")
            return None
    
    def _fetch_from_source(self, url: str, issue: str) -> Optional[Dict[str, Any]]:
        """从指定数据源获取开奖结果"""
        try:
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            content = response.text
            lines = content.strip().split('\n')
            
            # 解析数据查找指定期号
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 解析格式：期号 日期 开奖号码 试机号码
                match = re.match(r'(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})\s+(\d{3})', line)
                if match:
                    period, date, numbers, trial_numbers = match.groups()
                    
                    if period == issue:
                        result = {
                            'issue': period,
                            'draw_date': date,
                            'numbers': numbers,
                            'trial_numbers': trial_numbers,
                            'hundreds': int(numbers[0]),
                            'tens': int(numbers[1]),
                            'units': int(numbers[2]),
                            'trial_hundreds': int(trial_numbers[0]),
                            'trial_tens': int(trial_numbers[1]),
                            'trial_units': int(trial_numbers[2]),
                            'sum_value': sum(int(d) for d in numbers),
                            'span': max(int(d) for d in numbers) - min(int(d) for d in numbers)
                        }
                        
                        if self.validate_result_data(result):
                            return result
            
            return None
            
        except Exception as e:
            logger.error(f"从数据源 {url} 获取数据失败: {e}")
            return None
    
    def _get_latest_from_network(self) -> Optional[Dict[str, Any]]:
        """从网络获取最新开奖结果"""
        try:
            response = requests.get(self.data_sources['primary'], headers=self.headers, timeout=30)
            response.raise_for_status()
            
            content = response.text
            lines = content.strip().split('\n')
            
            # 倒序数据源，第一条就是最新的
            for line in lines[:5]:  # 只检查前5行
                line = line.strip()
                if not line:
                    continue
                
                match = re.match(r'(\d{7})\s+(\d{4}-\d{2}-\d{2})\s+(\d{3})\s+(\d{3})', line)
                if match:
                    period, date, numbers, trial_numbers = match.groups()
                    
                    result = {
                        'issue': period,
                        'draw_date': date,
                        'numbers': numbers,
                        'trial_numbers': trial_numbers,
                        'hundreds': int(numbers[0]),
                        'tens': int(numbers[1]),
                        'units': int(numbers[2]),
                        'trial_hundreds': int(trial_numbers[0]),
                        'trial_tens': int(trial_numbers[1]),
                        'trial_units': int(trial_numbers[2]),
                        'sum_value': sum(int(d) for d in numbers),
                        'span': max(int(d) for d in numbers) - min(int(d) for d in numbers)
                    }
                    
                    if self.validate_result_data(result):
                        # 保存到数据库
                        self._save_to_database(result)
                        return result
            
            return None
            
        except Exception as e:
            logger.error(f"从网络获取最新开奖结果失败: {e}")
            return None
    
    def _format_result(self, db_result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化数据库结果"""
        return {
            'issue': db_result.get('issue'),
            'draw_date': db_result.get('draw_date'),
            'numbers': f"{db_result.get('hundreds', 0)}{db_result.get('tens', 0)}{db_result.get('units', 0)}",
            'trial_numbers': f"{db_result.get('trial_hundreds', 0)}{db_result.get('trial_tens', 0)}{db_result.get('trial_units', 0)}",
            'hundreds': db_result.get('hundreds'),
            'tens': db_result.get('tens'),
            'units': db_result.get('units'),
            'trial_hundreds': db_result.get('trial_hundreds'),
            'trial_tens': db_result.get('trial_tens'),
            'trial_units': db_result.get('trial_units'),
            'sum_value': db_result.get('sum_value'),
            'span': db_result.get('span')
        }
    
    def _save_to_database(self, result: Dict[str, Any]) -> bool:
        """保存开奖结果到数据库"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT OR REPLACE INTO lottery_data 
                (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units,
                 sum_value, span, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
                conn.execute(sql, (
                    result['issue'],
                    result['draw_date'],
                    result['hundreds'],
                    result['tens'],
                    result['units'],
                    result['trial_hundreds'],
                    result['trial_tens'],
                    result['trial_units'],
                    result['sum_value'],
                    result['span']
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"保存开奖结果到数据库失败: {e}")
            return False
