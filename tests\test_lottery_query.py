"""
开奖查询引擎单元测试
测试 src.data.lottery_query 模块的功能

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import unittest
import tempfile
import os
import sqlite3
from unittest.mock import patch, MagicMock
import sys
sys.path.append('.')

from src.data.lottery_query import LotteryQueryEngine


class TestLotteryQueryEngine(unittest.TestCase):
    """开奖查询引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化测试数据库
        self._init_test_db()
        
        # 创建查询引擎实例
        self.engine = LotteryQueryEngine(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _init_test_db(self):
        """初始化测试数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建测试表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS lottery_data (
                    id INTEGER PRIMARY KEY,
                    issue TEXT UNIQUE,
                    draw_date TEXT,
                    hundreds INTEGER,
                    tens INTEGER,
                    units INTEGER,
                    trial_hundreds INTEGER,
                    trial_tens INTEGER,
                    trial_units INTEGER,
                    sum_value INTEGER,
                    span INTEGER,
                    updated_at TEXT
                )
            """)
            
            # 插入测试数据
            test_data = [
                ('2025001', '2025-01-01', 1, 2, 3, 4, 5, 6, 6, 2, '2025-01-01 10:00:00'),
                ('2025002', '2025-01-02', 4, 5, 6, 7, 8, 9, 15, 2, '2025-01-02 10:00:00'),
                ('2025003', '2025-01-03', 7, 8, 9, 0, 1, 2, 24, 2, '2025-01-03 10:00:00'),
            ]
            
            conn.executemany(
                """INSERT INTO lottery_data 
                   (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units, sum_value, span, updated_at) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                test_data
            )
            conn.commit()
    
    def test_get_result_by_issue_from_database(self):
        """测试从数据库获取开奖结果"""
        result = self.engine.get_result_by_issue('2025001')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['issue'], '2025001')
        self.assertEqual(result['numbers'], '123')
        self.assertEqual(result['trial_numbers'], '456')
        self.assertEqual(result['draw_date'], '2025-01-01')
        self.assertEqual(result['sum_value'], 6)
        self.assertEqual(result['span'], 2)
    
    def test_get_result_by_issue_nonexistent(self):
        """测试获取不存在的期号"""
        result = self.engine.get_result_by_issue('2025999')
        
        # 应该返回None（数据库中没有，网络查询也会失败）
        self.assertIsNone(result)
    
    def test_get_latest_result_from_database(self):
        """测试从数据库获取最新开奖结果"""
        result = self.engine.get_latest_result()
        
        self.assertIsNotNone(result)
        # 应该返回最新的期号（按日期和期号排序）
        self.assertEqual(result['issue'], '2025003')
        self.assertEqual(result['numbers'], '789')
    
    def test_validate_result_data_valid(self):
        """测试有效数据验证"""
        valid_data = {
            'issue': '2025001',
            'draw_date': '2025-01-01',
            'numbers': '123',
            'trial_numbers': '456',
            'hundreds': 1,
            'tens': 2,
            'units': 3,
            'sum_value': 6,
            'span': 2
        }
        
        is_valid = self.engine.validate_result_data(valid_data)
        self.assertTrue(is_valid)
    
    def test_validate_result_data_invalid_issue(self):
        """测试无效期号验证"""
        invalid_data = {
            'issue': '12345',  # 不是7位数字
            'draw_date': '2025-01-01',
            'numbers': '123'
        }
        
        is_valid = self.engine.validate_result_data(invalid_data)
        self.assertFalse(is_valid)
    
    def test_validate_result_data_invalid_numbers(self):
        """测试无效号码验证"""
        invalid_data = {
            'issue': '2025001',
            'draw_date': '2025-01-01',
            'numbers': '12'  # 不是3位数字
        }
        
        is_valid = self.engine.validate_result_data(invalid_data)
        self.assertFalse(is_valid)
    
    def test_validate_result_data_invalid_date(self):
        """测试无效日期验证"""
        invalid_data = {
            'issue': '2025001',
            'draw_date': '2025-13-01',  # 无效日期
            'numbers': '123'
        }
        
        is_valid = self.engine.validate_result_data(invalid_data)
        self.assertFalse(is_valid)
    
    def test_validate_result_data_missing_fields(self):
        """测试缺少必要字段验证"""
        incomplete_data = {
            'issue': '2025001',
            # 缺少 draw_date 和 numbers
        }
        
        is_valid = self.engine.validate_result_data(incomplete_data)
        self.assertFalse(is_valid)
    
    @patch('requests.get')
    def test_fetch_from_source_success(self, mock_get):
        """测试从网络数据源获取数据成功"""
        # 模拟网络响应
        mock_response = MagicMock()
        mock_response.text = "2025001 2025-01-01 123 456\n2025002 2025-01-02 789 012"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.engine._fetch_from_source("http://test.com", "2025001")
        
        self.assertIsNotNone(result)
        self.assertEqual(result['issue'], '2025001')
        self.assertEqual(result['numbers'], '123')
        self.assertEqual(result['trial_numbers'], '456')
    
    @patch('requests.get')
    def test_fetch_from_source_network_error(self, mock_get):
        """测试网络错误处理"""
        mock_get.side_effect = Exception("Network error")
        
        result = self.engine._fetch_from_source("http://test.com", "2025001")
        
        self.assertIsNone(result)
    
    @patch('requests.get')
    def test_fetch_from_source_invalid_format(self, mock_get):
        """测试无效格式数据处理"""
        # 模拟返回无效格式的数据
        mock_response = MagicMock()
        mock_response.text = "invalid data format"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.engine._fetch_from_source("http://test.com", "2025001")
        
        self.assertIsNone(result)
    
    def test_save_to_database(self):
        """测试保存数据到数据库"""
        new_data = {
            'issue': '2025004',
            'draw_date': '2025-01-04',
            'numbers': '012',
            'trial_numbers': '345',
            'hundreds': 0,
            'tens': 1,
            'units': 2,
            'trial_hundreds': 3,
            'trial_tens': 4,
            'trial_units': 5,
            'sum_value': 3,
            'span': 2
        }
        
        success = self.engine._save_to_database(new_data)
        self.assertTrue(success)
        
        # 验证数据已保存
        saved_result = self.engine.get_result_by_issue('2025004')
        self.assertIsNotNone(saved_result)
        self.assertEqual(saved_result['numbers'], '012')
    
    def test_save_to_database_duplicate(self):
        """测试保存重复数据（应该更新）"""
        # 尝试保存已存在的期号，但数据不同
        updated_data = {
            'issue': '2025001',  # 已存在的期号
            'draw_date': '2025-01-01',
            'numbers': '999',  # 不同的号码
            'trial_numbers': '888',
            'hundreds': 9,
            'tens': 9,
            'units': 9,
            'trial_hundreds': 8,
            'trial_tens': 8,
            'trial_units': 8,
            'sum_value': 27,
            'span': 0
        }
        
        success = self.engine._save_to_database(updated_data)
        self.assertTrue(success)
        
        # 验证数据已更新
        updated_result = self.engine.get_result_by_issue('2025001')
        self.assertEqual(updated_result['numbers'], '999')
    
    def test_format_result(self):
        """测试结果格式化"""
        db_result = {
            'issue': '2025001',
            'draw_date': '2025-01-01',
            'hundreds': 1,
            'tens': 2,
            'units': 3,
            'trial_hundreds': 4,
            'trial_tens': 5,
            'trial_units': 6,
            'sum_value': 6,
            'span': 2
        }
        
        formatted = self.engine._format_result(db_result)
        
        self.assertEqual(formatted['issue'], '2025001')
        self.assertEqual(formatted['numbers'], '123')
        self.assertEqual(formatted['trial_numbers'], '456')
        self.assertEqual(formatted['hundreds'], 1)
        self.assertEqual(formatted['tens'], 2)
        self.assertEqual(formatted['units'], 3)
    
    def test_get_connection(self):
        """测试数据库连接"""
        conn = self.engine.get_connection()
        self.assertIsNotNone(conn)
        
        # 测试连接是否可用
        cursor = conn.execute("SELECT COUNT(*) FROM lottery_data")
        count = cursor.fetchone()[0]
        self.assertGreater(count, 0)
        
        conn.close()


class TestLotteryQueryEngineIntegration(unittest.TestCase):
    """开奖查询引擎集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        self.engine = LotteryQueryEngine(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    @patch('requests.get')
    def test_full_query_workflow_with_network_fallback(self, mock_get):
        """测试完整查询流程（数据库无数据时网络获取）"""
        # 模拟网络响应
        mock_response = MagicMock()
        mock_response.text = "2025001 2025-01-01 123 456"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # 查询不存在的期号（应该触发网络查询）
        result = self.engine.get_result_by_issue('2025001')
        
        # 验证网络查询被调用
        self.assertTrue(mock_get.called)
        
        # 验证结果正确
        if result:  # 网络查询可能失败
            self.assertEqual(result['issue'], '2025001')
            self.assertEqual(result['numbers'], '123')
            
            # 验证数据已保存到数据库
            db_result = self.engine.get_result_by_issue('2025001')
            self.assertIsNotNone(db_result)


if __name__ == '__main__':
    unittest.main()
