"""
复盘功能API路由
提供手动复盘、复盘历史查询等功能

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime, timedelta
import asyncio

# 导入复盘相关模块
from src.automation.closed_loop_system import ClosedLoopSystem
from src.analysis.review_engine import ReviewEngine
from src.data.review_data_access import ReviewDataAccess
from src.data.lottery_query import LotteryQueryEngine

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/review", tags=["复盘功能"])

# 全局变量存储复盘状态
review_status = {
    "is_running": False,
    "last_run": None,
    "last_result": None,
    "error": None
}


@router.post("/manual")
async def manual_review(background_tasks: BackgroundTasks):
    """
    手动触发复盘
    """
    try:
        global review_status
        
        # 检查是否已经在运行
        if review_status["is_running"]:
            raise HTTPException(
                status_code=409, 
                detail="复盘正在进行中，请稍后再试"
            )
        
        # 标记为运行中
        review_status["is_running"] = True
        review_status["error"] = None
        
        # 在后台执行复盘
        background_tasks.add_task(execute_manual_review)
        
        return {
            "success": True,
            "message": "手动复盘已启动",
            "timestamp": datetime.now().isoformat(),
            "status": "running"
        }
        
    except Exception as e:
        logger.error(f"启动手动复盘失败: {e}")
        review_status["is_running"] = False
        review_status["error"] = str(e)
        raise HTTPException(status_code=500, detail=f"启动复盘失败: {e}")


async def execute_manual_review():
    """执行手动复盘的后台任务"""
    global review_status
    
    try:
        logger.info("🔍 开始执行手动复盘...")
        
        # 创建闭环系统实例
        system = ClosedLoopSystem()
        
        # 执行复盘
        result = system.auto_review()
        
        # 更新状态
        review_status["is_running"] = False
        review_status["last_run"] = datetime.now().isoformat()
        review_status["last_result"] = result
        review_status["error"] = None
        
        logger.info(f"✅ 手动复盘完成，结果: {result}")
        
    except Exception as e:
        logger.error(f"❌ 手动复盘执行失败: {e}")
        review_status["is_running"] = False
        review_status["error"] = str(e)
        review_status["last_run"] = datetime.now().isoformat()
        review_status["last_result"] = False


@router.get("/status")
async def get_review_status():
    """
    获取复盘状态
    """
    try:
        return {
            "success": True,
            "data": review_status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取复盘状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {e}")


@router.get("/history")
async def get_review_history(
    limit: int = 10,
    days: int = 30
):
    """
    获取复盘历史记录
    
    Args:
        limit: 返回记录数量限制
        days: 查询天数范围
    """
    try:
        # 创建复盘数据访问实例
        review_da = ReviewDataAccess()
        
        # 获取最近的复盘记录
        recent_reviews = review_da.get_recent_reviews(limit=limit)
        
        # 获取统计信息
        stats = review_da.get_accuracy_statistics(days=days)
        
        return {
            "success": True,
            "data": {
                "reviews": recent_reviews,
                "statistics": stats,
                "total_count": len(recent_reviews)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取复盘历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {e}")


@router.get("/history/{issue}")
async def get_review_by_issue(issue: str):
    """
    根据期号获取复盘结果
    
    Args:
        issue: 期号
    """
    try:
        # 创建复盘数据访问实例
        review_da = ReviewDataAccess()
        
        # 查询指定期号的复盘结果
        result = review_da.get_review_by_issue(issue)
        
        if not result:
            raise HTTPException(
                status_code=404, 
                detail=f"未找到期号 {issue} 的复盘记录"
            )
        
        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询期号 {issue} 复盘结果失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {e}")


@router.post("/test")
async def test_review_components():
    """
    测试复盘组件功能
    """
    try:
        test_results = {}
        
        # 测试复盘引擎
        try:
            engine = ReviewEngine()
            test_predictions = ['123', '124', '125', '321']
            test_actual = '123'
            comparison = engine.compare_predictions(test_predictions, test_actual)
            test_results["review_engine"] = {
                "status": "success",
                "accuracy": comparison.get("overall_accuracy", 0)
            }
        except Exception as e:
            test_results["review_engine"] = {
                "status": "error",
                "error": str(e)
            }
        
        # 测试开奖查询
        try:
            lottery_query = LotteryQueryEngine()
            # 尝试获取最新开奖结果
            latest = lottery_query.get_latest_result()
            test_results["lottery_query"] = {
                "status": "success",
                "has_data": latest is not None
            }
        except Exception as e:
            test_results["lottery_query"] = {
                "status": "error", 
                "error": str(e)
            }
        
        # 测试数据访问
        try:
            review_da = ReviewDataAccess()
            stats = review_da.get_accuracy_statistics(days=7)
            test_results["data_access"] = {
                "status": "success",
                "total_reviews": stats.get("total_reviews", 0)
            }
        except Exception as e:
            test_results["data_access"] = {
                "status": "error",
                "error": str(e)
            }
        
        # 计算总体状态
        all_success = all(
            result.get("status") == "success" 
            for result in test_results.values()
        )
        
        return {
            "success": True,
            "data": {
                "overall_status": "healthy" if all_success else "degraded",
                "components": test_results
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"测试复盘组件失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试失败: {e}")


@router.get("/config")
async def get_review_config():
    """
    获取复盘配置信息
    """
    try:
        # 创建闭环系统实例获取配置
        system = ClosedLoopSystem()
        
        config_info = {
            "auto_review_time": system.config.get("review_time", "22:00"),
            "prediction_time": system.config.get("prediction_time", "21:40"),
            "optimization_time": system.config.get("optimization_time", "02:00"),
            "max_retries": system.config.get("max_retries", 3),
            "retry_delay": system.config.get("retry_delay", 300)
        }
        
        return {
            "success": True,
            "data": config_info,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取复盘配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {e}")


@router.get("/next-schedule")
async def get_next_review_schedule():
    """
    获取下次自动复盘的时间
    """
    try:
        # 创建闭环系统实例获取配置
        system = ClosedLoopSystem()
        review_time = system.config.get("review_time", "22:00")
        
        # 计算下次复盘时间
        now = datetime.now()
        today_review = datetime.strptime(f"{now.date()} {review_time}", "%Y-%m-%d %H:%M")
        
        if now > today_review:
            # 今天的复盘时间已过，下次是明天
            next_review = today_review + timedelta(days=1)
        else:
            # 今天的复盘时间还没到
            next_review = today_review
        
        return {
            "success": True,
            "data": {
                "next_review_time": next_review.isoformat(),
                "configured_time": review_time,
                "hours_until_next": (next_review - now).total_seconds() / 3600
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取下次复盘时间失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取时间失败: {e}")
