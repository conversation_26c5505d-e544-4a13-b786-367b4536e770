import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Table, 
  Tag, 
  Space, 
  Statistic, 
  Row, 
  Col, 
  Alert, 
  Spin, 
  Modal,
  Progress,
  Descriptions,
  message,
  Tooltip
} from 'antd';
import { 
  PlayCircleOutlined, 
  ReloadOutlined, 
  HistoryOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

interface ReviewResult {
  id: number;
  issue: string;
  review_date: string;
  actual_number: string;
  total_predictions: number;
  direct_hits: number;
  group_hits: number;
  overall_accuracy: number;
  created_at: string;
}

interface ReviewStatus {
  is_running: boolean;
  last_run: string | null;
  last_result: boolean | null;
  error: string | null;
}

interface ReviewStatistics {
  total_reviews: number;
  average_accuracy: number;
  best_accuracy: number;
  worst_accuracy: number;
}

const ReviewPanel: React.FC = () => {
  const [reviewHistory, setReviewHistory] = useState<ReviewResult[]>([]);
  const [reviewStatus, setReviewStatus] = useState<ReviewStatus>({
    is_running: false,
    last_run: null,
    last_result: null,
    error: null
  });
  const [statistics, setStatistics] = useState<ReviewStatistics>({
    total_reviews: 0,
    average_accuracy: 0,
    best_accuracy: 0,
    worst_accuracy: 0
  });
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedReview, setSelectedReview] = useState<ReviewResult | null>(null);

  // 获取复盘历史
  const fetchReviewHistory = async () => {
    try {
      const response = await fetch('/api/review/history?limit=20&days=30');
      const data = await response.json();
      
      if (data.success) {
        setReviewHistory(data.data.reviews || []);
        setStatistics(data.data.statistics || {
          total_reviews: 0,
          average_accuracy: 0,
          best_accuracy: 0,
          worst_accuracy: 0
        });
      }
    } catch (error) {
      console.error('获取复盘历史失败:', error);
    }
  };

  // 获取复盘状态
  const fetchReviewStatus = async () => {
    try {
      const response = await fetch('/api/review/status');
      const data = await response.json();
      
      if (data.success) {
        setReviewStatus(data.data);
      }
    } catch (error) {
      console.error('获取复盘状态失败:', error);
    }
  };

  // 手动触发复盘
  const triggerManualReview = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/review/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (data.success) {
        message.success('手动复盘已启动，请稍后查看结果');
        // 开始轮询状态
        startStatusPolling();
      } else {
        message.error(data.detail || '启动复盘失败');
      }
    } catch (error) {
      console.error('触发手动复盘失败:', error);
      message.error('启动复盘失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始状态轮询
  const startStatusPolling = () => {
    const pollInterval = setInterval(async () => {
      await fetchReviewStatus();
      
      // 如果复盘完成，停止轮询并刷新历史
      if (!reviewStatus.is_running) {
        clearInterval(pollInterval);
        await fetchReviewHistory();
        
        if (reviewStatus.last_result) {
          message.success('复盘执行完成！');
        } else if (reviewStatus.error) {
          message.error(`复盘执行失败: ${reviewStatus.error}`);
        }
      }
    }, 2000); // 每2秒检查一次

    // 30秒后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30000);
  };

  // 查看详细信息
  const showReviewDetail = async (review: ReviewResult) => {
    try {
      const response = await fetch(`/api/review/history/${review.issue}`);
      const data = await response.json();
      
      if (data.success) {
        setSelectedReview(data.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取复盘详情失败:', error);
      message.error('获取详情失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchReviewHistory();
    fetchReviewStatus();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '期号',
      dataIndex: 'issue',
      key: 'issue',
      width: 100,
    },
    {
      title: '复盘日期',
      dataIndex: 'review_date',
      key: 'review_date',
      width: 120,
    },
    {
      title: '实际开奖',
      dataIndex: 'actual_number',
      key: 'actual_number',
      width: 100,
      render: (text: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace', fontSize: '14px' }}>
          {text}
        </Tag>
      ),
    },
    {
      title: '预测总数',
      dataIndex: 'total_predictions',
      key: 'total_predictions',
      width: 100,
    },
    {
      title: '直选命中',
      dataIndex: 'direct_hits',
      key: 'direct_hits',
      width: 100,
      render: (hits: number, record: ReviewResult) => (
        <span style={{ color: hits > 0 ? '#52c41a' : '#8c8c8c' }}>
          {hits}/{record.total_predictions}
        </span>
      ),
    },
    {
      title: '组选命中',
      dataIndex: 'group_hits',
      key: 'group_hits',
      width: 100,
      render: (hits: number, record: ReviewResult) => (
        <span style={{ color: hits > 0 ? '#52c41a' : '#8c8c8c' }}>
          {hits}/{record.total_predictions}
        </span>
      ),
    },
    {
      title: '综合准确率',
      dataIndex: 'overall_accuracy',
      key: 'overall_accuracy',
      width: 120,
      render: (accuracy: number) => (
        <Tag color={accuracy > 0.3 ? 'green' : accuracy > 0.1 ? 'orange' : 'red'}>
          {(accuracy * 100).toFixed(1)}%
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: ReviewResult) => (
        <Button 
          type="link" 
          size="small"
          onClick={() => showReviewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 复盘控制面板 */}
      <Card title="复盘控制" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title="总复盘次数"
              value={statistics.total_reviews}
              prefix={<HistoryOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均准确率"
              value={(statistics.average_accuracy * 100).toFixed(1)}
              suffix="%"
              precision={1}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="最佳准确率"
              value={(statistics.best_accuracy * 100).toFixed(1)}
              suffix="%"
              precision={1}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="最差准确率"
              value={(statistics.worst_accuracy * 100).toFixed(1)}
              suffix="%"
              precision={1}
            />
          </Col>
        </Row>

        <div style={{ marginTop: '24px' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={loading || reviewStatus.is_running}
              onClick={triggerManualReview}
              disabled={reviewStatus.is_running}
            >
              {reviewStatus.is_running ? '复盘进行中...' : '手动复盘'}
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchReviewHistory();
                fetchReviewStatus();
              }}
            >
              刷新数据
            </Button>

            <Tooltip title="自动复盘时间: 每天22:00">
              <Button icon={<ClockCircleOutlined />}>
                定时设置
              </Button>
            </Tooltip>
          </Space>
        </div>

        {/* 状态提示 */}
        {reviewStatus.is_running && (
          <Alert
            message="复盘正在进行中"
            description="请稍等，复盘过程通常需要几秒钟时间..."
            type="info"
            showIcon
            style={{ marginTop: '16px' }}
          />
        )}

        {reviewStatus.error && (
          <Alert
            message="复盘执行失败"
            description={reviewStatus.error}
            type="error"
            showIcon
            style={{ marginTop: '16px' }}
          />
        )}

        {reviewStatus.last_run && !reviewStatus.is_running && (
          <Alert
            message={`上次复盘: ${new Date(reviewStatus.last_run).toLocaleString()}`}
            description={`结果: ${reviewStatus.last_result ? '成功' : '失败'}`}
            type={reviewStatus.last_result ? 'success' : 'warning'}
            showIcon
            style={{ marginTop: '16px' }}
          />
        )}
      </Card>

      {/* 复盘历史 */}
      <Card title="复盘历史" extra={
        <Space>
          <Tag color="blue">最近30天</Tag>
          <Tag color="green">共{reviewHistory.length}条记录</Tag>
        </Space>
      }>
        <Table
          columns={columns}
          dataSource={reviewHistory}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title={`复盘详情 - 期号 ${selectedReview?.issue}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedReview && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="期号">{selectedReview.issue}</Descriptions.Item>
            <Descriptions.Item label="复盘日期">{selectedReview.review_date}</Descriptions.Item>
            <Descriptions.Item label="实际开奖">
              <Tag color="blue" style={{ fontSize: '16px', padding: '4px 8px' }}>
                {selectedReview.actual_number}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="预测总数">{selectedReview.total_predictions}</Descriptions.Item>
            <Descriptions.Item label="直选命中">
              <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                {selectedReview.direct_hits}/{selectedReview.total_predictions}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="组选命中">
              <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                {selectedReview.group_hits}/{selectedReview.total_predictions}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="综合准确率" span={2}>
              <Progress 
                percent={selectedReview.overall_accuracy * 100} 
                format={(percent) => `${percent?.toFixed(1)}%`}
                strokeColor={selectedReview.overall_accuracy > 0.3 ? '#52c41a' : selectedReview.overall_accuracy > 0.1 ? '#faad14' : '#ff4d4f'}
              />
            </Descriptions.Item>
            <Descriptions.Item label="创建时间" span={2}>
              {new Date(selectedReview.created_at).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ReviewPanel;
