{"tests/test_cache_optimizer.py": true, "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_empty_results": true, "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_with_data": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_direct_hit": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_empty_input": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_group_hit": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_invalid_format": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_position_hits": true, "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary": true, "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary_empty_data": true, "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions": true, "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions_nonexistent_issue": true, "tests/test_review_engine.py::TestReviewEngine::test_lottery_query_integration": true}