"""
复盘功能集成测试
测试完整的复盘流程、数据存储、性能等

Author: Augment Code AI Assistant
Date: 2025-08-10
"""

import unittest
import tempfile
import os
import sqlite3
import time
from unittest.mock import patch, MagicMock
import sys
sys.path.append('.')

from src.automation.closed_loop_system import ClosedLoopSystem
from src.analysis.review_engine import ReviewEngine
from src.analysis.accuracy_calculator import AccuracyCalculator
from src.data.lottery_query import LotteryQueryEngine
from src.data.review_data_access import ReviewDataAccess


class TestReviewIntegration(unittest.TestCase):
    """复盘功能集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化测试数据库
        self._init_test_databases()
        
        # 创建组件实例
        self.review_engine = ReviewEngine(self.db_path)
        self.accuracy_calculator = AccuracyCalculator()
        self.lottery_query = LotteryQueryEngine(self.db_path)
        self.review_data_access = ReviewDataAccess(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _init_test_databases(self):
        """初始化测试数据库"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建预测数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fusion_predictions (
                    id INTEGER PRIMARY KEY,
                    issue TEXT,
                    combination TEXT,
                    rank INTEGER,
                    timestamp TEXT
                )
            """)
            
            # 创建开奖数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS lottery_data (
                    id INTEGER PRIMARY KEY,
                    issue TEXT UNIQUE,
                    draw_date TEXT,
                    hundreds INTEGER,
                    tens INTEGER,
                    units INTEGER,
                    trial_hundreds INTEGER,
                    trial_tens INTEGER,
                    trial_units INTEGER,
                    sum_value INTEGER,
                    span INTEGER,
                    updated_at TEXT
                )
            """)
            
            # 插入测试预测数据
            test_predictions = [
                ('2025001', '123', 1, '2025-01-01 10:00:00'),
                ('2025001', '124', 2, '2025-01-01 10:00:00'),
                ('2025001', '125', 3, '2025-01-01 10:00:00'),
                ('2025001', '321', 4, '2025-01-01 10:00:00'),
                ('2025001', '456', 5, '2025-01-01 10:00:00'),
            ]
            
            conn.executemany(
                "INSERT INTO fusion_predictions (issue, combination, rank, timestamp) VALUES (?, ?, ?, ?)",
                test_predictions
            )
            
            # 插入测试开奖数据
            test_lottery = [
                ('2025001', '2025-01-01', 1, 2, 3, 4, 5, 6, 6, 2, '2025-01-01 20:00:00'),
            ]
            
            conn.executemany(
                """INSERT INTO lottery_data 
                   (issue, draw_date, hundreds, tens, units, trial_hundreds, trial_tens, trial_units, sum_value, span, updated_at) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                test_lottery
            )
            
            conn.commit()
    
    def test_complete_review_workflow(self):
        """测试完整复盘工作流程"""
        issue = '2025001'
        
        # 1. 获取预测数据
        predictions = self.review_engine.get_recent_predictions(issue, limit=10)
        self.assertGreater(len(predictions), 0)
        self.assertEqual(predictions[0], '123')
        
        # 2. 获取开奖数据
        actual_result = self.lottery_query.get_result_by_issue(issue)
        self.assertIsNotNone(actual_result)
        self.assertEqual(actual_result['numbers'], '123')
        
        # 3. 执行复盘对比
        comparison_result = self.review_engine.compare_predictions(predictions, actual_result['numbers'])
        self.assertIsNotNone(comparison_result)
        self.assertEqual(comparison_result['actual_number'], '123')
        self.assertEqual(comparison_result['direct_hits'], 1)  # '123'直选命中
        
        # 4. 生成复盘摘要
        summary = self.review_engine.generate_review_summary(comparison_result)
        self.assertIn('实际开奖: 123', summary)
        self.assertIn('直选命中: 1/5', summary)
        
        # 5. 保存复盘结果
        review_data = {
            'issue': issue,
            'actual_number': actual_result['numbers'],
            'predicted_numbers': predictions,
            'comparison_details': comparison_result,
            'summary': summary,
            'improvement_suggestions': ['测试建议']
        }
        
        save_success = self.review_data_access.save_review_result(review_data)
        self.assertTrue(save_success)
        
        # 6. 验证保存的数据
        saved_result = self.review_data_access.get_review_by_issue(issue)
        self.assertIsNotNone(saved_result)
        self.assertEqual(saved_result['issue'], issue)
        self.assertEqual(saved_result['actual_number'], '123')
    
    def test_accuracy_calculator_integration(self):
        """测试准确率计算器集成"""
        predictions = ['123', '124', '125', '321', '456']
        actual = '123'
        
        # 测试所有维度的准确率计算
        comprehensive = self.accuracy_calculator.comprehensive_accuracy(predictions, actual)
        
        self.assertEqual(comprehensive['total_predictions'], 5)
        self.assertIn('dimensions', comprehensive)
        
        # 验证各维度结果
        dimensions = comprehensive['dimensions']
        self.assertEqual(dimensions['direct']['hits'], 1)  # 直选命中1个
        self.assertEqual(dimensions['group']['hits'], 2)   # 组选命中2个（123和321）
        
        # 验证位置命中
        position_hits = dimensions['position']['hits']
        self.assertEqual(position_hits['hundreds'], 2)  # 百位1命中2个（123, 124, 125中的123和124）
        self.assertEqual(position_hits['tens'], 1)      # 十位2命中1个（123）
        self.assertEqual(position_hits['units'], 1)     # 个位3命中1个（123）
    
    def test_performance_benchmark(self):
        """测试性能基准 - 确保复盘计算<5秒"""
        issue = '2025001'
        
        start_time = time.time()
        
        # 执行完整复盘流程
        predictions = self.review_engine.get_recent_predictions(issue, limit=20)
        actual_result = self.lottery_query.get_result_by_issue(issue)
        
        if actual_result and predictions:
            comparison_result = self.review_engine.compare_predictions(predictions, actual_result['numbers'])
            summary = self.review_engine.generate_review_summary(comparison_result)
            
            # 执行所有维度的准确率计算
            comprehensive = self.accuracy_calculator.comprehensive_accuracy(predictions, actual_result['numbers'])
            
            # 保存结果
            review_data = {
                'issue': issue,
                'actual_number': actual_result['numbers'],
                'predicted_numbers': predictions,
                'comparison_details': comparison_result,
                'summary': summary,
                'improvement_suggestions': []
            }
            self.review_data_access.save_review_result(review_data)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证执行时间 < 5秒
        self.assertLess(execution_time, 5.0, f"复盘计算耗时 {execution_time:.2f}秒，超过5秒限制")
        print(f"复盘计算耗时: {execution_time:.3f}秒")
    
    def test_data_storage_and_query(self):
        """测试数据存储和查询功能"""
        # 准备测试数据
        test_reviews = []
        for i in range(5):
            issue = f'202500{i+1}'
            review_data = {
                'issue': issue,
                'actual_number': f'{i}{i}{i}',
                'predicted_numbers': [f'{i}{i}{i}', f'{i}{i}{i+1}'],
                'comparison_details': {
                    'total_predictions': 2,
                    'direct_hits': 1,
                    'group_hits': 1,
                    'position_hits': {'hundreds': 2, 'tens': 2, 'units': 1},
                    'direct_accuracy': 0.5,
                    'group_accuracy': 0.5,
                    'position_accuracy': {'hundreds': 1.0, 'tens': 1.0, 'units': 0.5},
                    'overall_accuracy': 0.6
                },
                'summary': f'测试摘要{i}',
                'improvement_suggestions': [f'建议{i}']
            }
            test_reviews.append(review_data)
            
            # 保存数据
            success = self.review_data_access.save_review_result(review_data)
            self.assertTrue(success)
        
        # 测试查询功能
        # 1. 按期号查询
        result = self.review_data_access.get_review_by_issue('2025001')
        self.assertIsNotNone(result)
        self.assertEqual(result['actual_number'], '000')
        
        # 2. 获取最近记录
        recent_reviews = self.review_data_access.get_recent_reviews(limit=3)
        self.assertEqual(len(recent_reviews), 3)
        
        # 3. 获取统计信息
        stats = self.review_data_access.get_accuracy_statistics(days=30)
        self.assertEqual(stats['total_reviews'], 5)
        self.assertAlmostEqual(stats['average_accuracy'], 0.6, places=2)
    
    def test_error_handling_and_recovery(self):
        """测试错误处理和恢复机制"""
        # 测试无效期号
        result = self.review_engine.get_recent_predictions('invalid_issue')
        self.assertEqual(len(result), 0)
        
        # 测试无效开奖数据
        comparison = self.review_engine.compare_predictions(['123'], '')
        self.assertEqual(comparison['total_predictions'], 0)
        
        # 测试空预测列表
        comparison = self.review_engine.compare_predictions([], '123')
        self.assertEqual(comparison['total_predictions'], 0)
        
        # 测试数据验证
        invalid_data = {
            'issue': 'invalid',
            'actual_number': '',
            'predicted_numbers': [],
            'comparison_details': {}
        }
        save_success = self.review_data_access.save_review_result(invalid_data)
        self.assertFalse(save_success)
    
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        # 生成大量预测数据
        large_predictions = [f"{i:03d}" for i in range(100)]
        actual = '500'
        
        start_time = time.time()
        
        # 执行准确率计算
        comprehensive = self.accuracy_calculator.comprehensive_accuracy(large_predictions, actual)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证结果正确性
        self.assertEqual(comprehensive['total_predictions'], 100)
        self.assertEqual(comprehensive['dimensions']['direct']['hits'], 1)  # 只有'500'命中
        
        # 验证性能（大数据集也应该很快）
        self.assertLess(execution_time, 1.0, f"大数据集计算耗时 {execution_time:.2f}秒，超过1秒限制")
        print(f"大数据集(100个预测)计算耗时: {execution_time:.3f}秒")


class TestClosedLoopSystemIntegration(unittest.TestCase):
    """闭环系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        # 初始化测试数据
        self._init_test_data()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def _init_test_data(self):
        """初始化测试数据"""
        with sqlite3.connect(self.db_path) as conn:
            # 创建必要的表结构
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fusion_predictions (
                    id INTEGER PRIMARY KEY,
                    issue TEXT,
                    combination TEXT,
                    rank INTEGER,
                    timestamp TEXT
                )
            """)
            
            # 插入测试数据
            conn.execute(
                "INSERT INTO fusion_predictions (issue, combination, rank, timestamp) VALUES (?, ?, ?, ?)",
                ('2025001', '123', 1, '2025-01-01 10:00:00')
            )
            conn.commit()
    
    @patch('src.data.lottery_query.LotteryQueryEngine.get_result_by_issue')
    def test_closed_loop_review_integration(self, mock_get_result):
        """测试闭环系统复盘集成"""
        # 模拟开奖查询返回
        mock_get_result.return_value = {
            'issue': '2025001',
            'numbers': '123',
            'draw_date': '2025-01-01'
        }
        
        # 创建闭环系统实例（使用测试数据库）
        system = ClosedLoopSystem()
        system.fusion_db_path = self.db_path
        system.review_engine = ReviewEngine(self.db_path)
        system.review_data_access = ReviewDataAccess(self.db_path)
        
        # 执行复盘
        result = system._perform_review()
        
        # 验证复盘结果
        self.assertIsNotNone(result)
        self.assertIn('accuracy', result)
        self.assertIn('actual_number', result)
        self.assertIn('comparison_details', result)


if __name__ == '__main__':
    # 运行测试并输出详细信息
    unittest.main(verbosity=2)
